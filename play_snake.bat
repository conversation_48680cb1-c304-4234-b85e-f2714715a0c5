@echo off
chcp 65001 >nul
title 贪吃蛇游戏

echo ========================================
echo 🐍 贪吃蛇游戏启动器 🐍
echo ========================================
echo.

:: 检查Go环境
echo [检查] 正在检测Go语言环境...
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Go语言环境
    echo.
    echo 📥 请先安装Go语言：
    echo 1. 访问 https://golang.org/dl/
    echo 2. 下载Windows 64位安装包
    echo 3. 安装后重新运行此脚本
    echo.
    echo 💡 或者运行 install_go.bat 自动安装
    echo.
    pause
    exit /b 1
)

for /f "tokens=3" %%i in ('go version 2^>nul') do set GO_VERSION=%%i
echo ✅ Go环境正常: %GO_VERSION%
echo.

:: 检查已编译的游戏
if exist simple_snake.exe (
    echo ✅ 找到编译好的游戏: simple_snake.exe
    goto :run_game
)

if exist snake-console.exe (
    echo ✅ 找到编译好的游戏: snake-console.exe
    goto :run_console
)

:: 编译游戏
echo [编译] 正在编译贪吃蛇游戏...

if exist simple_snake.go (
    echo 编译简单版本...
    go build -o simple_snake.exe simple_snake.go 2>nul
    if exist simple_snake.exe (
        echo ✅ 简单版本编译成功！
        goto :run_game
    )
)

if exist snake_console.go (
    echo 编译控制台版本...
    go build -o snake-console.exe snake_console.go 2>nul
    if exist snake-console.exe (
        echo ✅ 控制台版本编译成功！
        goto :run_console
    )
)

echo ❌ 编译失败，请检查源代码文件
pause
exit /b 1

:run_game
echo.
echo 🎮 启动简单贪吃蛇游戏
echo.
echo 📋 游戏操作说明：
echo • w - 向上移动
echo • a - 向左移动  
echo • s - 向下移动
echo • d - 向右移动
echo • r - 重新开始游戏
echo • q - 退出游戏
echo.
echo 🎯 游戏规则：
echo • 控制蛇(O)吃食物(*)
echo • 每吃一个食物得10分
echo • 撞墙或撞到自己身体游戏结束
echo.
echo 💡 提示：输入方向键后按回车确认
echo.
pause
cls
simple_snake.exe
goto :end

:run_console
echo.
echo 🎮 启动控制台贪吃蛇游戏
echo.
echo 📋 游戏操作说明：
echo • WASD 控制方向
echo • R - 重新开始
echo • Q - 退出游戏
echo.
pause
cls
snake-console.exe
goto :end

:end
echo.
echo 🎉 游戏结束，感谢游戏！
echo.
echo 如果您喜欢这个游戏，可以：
echo • 再次运行此脚本继续游戏
echo • 查看源代码学习Go语言编程
echo • 修改游戏参数自定义难度
echo.
pause
