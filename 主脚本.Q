//==============================================================================
// 剑网三缘起茶馆日常自动化脚本
// 版本: 1.0
// 作者: AI Assistant
// 适用于: 按键精灵2014
// 功能: 自动完成剑网三缘起茶馆日常任务
//==============================================================================

// 全局变量定义
Dim 游戏窗口句柄, 配置文件路径, 日志文件路径
Dim 任务完成状态(20), 当前任务索引, 脚本运行状态
Dim 基础延时, 长延时, 短延时

// 坐标配置 (需要根据实际游戏界面调整)
Dim 赵云睿坐标X, 赵云睿坐标Y
Dim 茶馆小童坐标X, 茶馆小童坐标Y
Dim 任务面板坐标X, 任务面板坐标Y
Dim 背包坐标X, 背包坐标Y

// 初始化脚本
Sub 初始化脚本()
    配置文件路径 = "config.txt"
    日志文件路径 = "jx3_log.txt"
    脚本运行状态 = True
    当前任务索引 = 0
    
    // 设置基础延时
    基础延时 = 1000
    长延时 = 3000
    短延时 = 500
    
    // 读取配置文件
    Call 读取配置文件()
    
    // 查找游戏窗口
    Call 查找游戏窗口()
    
    // 写入日志
    Call 写入日志("脚本初始化完成")
End Sub

// 读取配置文件
Sub 读取配置文件()
    Dim fso
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    If fso.FileExists(配置文件路径) Then
        // 读取坐标配置
        赵云睿坐标X = ReadIni(配置文件路径, "坐标", "赵云睿X", "500")
        赵云睿坐标Y = ReadIni(配置文件路径, "坐标", "赵云睿Y", "300")
        茶馆小童坐标X = ReadIni(配置文件路径, "坐标", "茶馆小童X", "520")
        茶馆小童坐标Y = ReadIni(配置文件路径, "坐标", "茶馆小童Y", "320")
        背包坐标X = ReadIni(配置文件路径, "坐标", "背包X", "800")
        背包坐标Y = ReadIni(配置文件路径, "坐标", "背包Y", "200")
        
        // 读取延时配置
        基础延时 = ReadIni(配置文件路径, "延时", "基础延时", "1000")
        长延时 = ReadIni(配置文件路径, "延时", "长延时", "3000")
        短延时 = ReadIni(配置文件路径, "延时", "短延时", "500")
    Else
        Call 创建默认配置文件()
    End If
End Sub

// 创建默认配置文件
Sub 创建默认配置文件()
    WriteIni 配置文件路径, "坐标", "赵云睿X", "500"
    WriteIni 配置文件路径, "坐标", "赵云睿Y", "300"
    WriteIni 配置文件路径, "坐标", "茶馆小童X", "520"
    WriteIni 配置文件路径, "坐标", "茶馆小童Y", "320"
    WriteIni 配置文件路径, "坐标", "背包X", "800"
    WriteIni 配置文件路径, "坐标", "背包Y", "200"
    WriteIni 配置文件路径, "延时", "基础延时", "1000"
    WriteIni 配置文件路径, "延时", "长延时", "3000"
    WriteIni 配置文件路径, "延时", "短延时", "500"
    Call 写入日志("已创建默认配置文件")
End Sub

// 查找游戏窗口
Sub 查找游戏窗口()
    游戏窗口句柄 = FindWindow("", "剑网3")
    If 游戏窗口句柄 = 0 Then
        游戏窗口句柄 = FindWindow("", "JX3")
    End If
    
    If 游戏窗口句柄 = 0 Then
        MessageBox "未找到游戏窗口，请确保游戏已启动"
        脚本运行状态 = False
        Exit Sub
    End If
    
    // 激活游戏窗口
    Call Plugin.Window.Active(游戏窗口句柄)
    Delay 基础延时
    Call 写入日志("已找到并激活游戏窗口")
End Sub

// 写入日志
Sub 写入日志(日志内容)
    Dim 当前时间, fso, 文件对象
    当前时间 = Now()
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set 文件对象 = fso.OpenTextFile(日志文件路径, 8, True)
    文件对象.WriteLine "[" & 当前时间 & "] " & 日志内容
    文件对象.Close
End Sub

// 安全点击函数
Sub 安全点击(x, y, 描述)
    If 脚本运行状态 = False Then Exit Sub
    
    Call 写入日志("点击: " & 描述 & " 坐标(" & x & "," & y & ")")
    MoveTo x, y
    Delay 短延时
    LeftClick 1
    Delay 基础延时
End Sub

// 移动到指定区域函数
Sub 移动到指定区域(目标X, 目标Y, 描述)
    Call 写入日志("移动到: " & 描述)
    
    // 使用右键移动
    MoveTo 目标X, 目标Y
    RightClick 1
    Delay 长延时 * 2  // 等待移动完成
End Sub

// 主要任务执行函数
Sub 执行茶馆日常()
    Call 写入日志("开始执行茶馆日常任务")
    
    // 1. 接取茶馆任务
    Call 接取茶馆任务()
    
    // 2. 执行新人茶馆任务
    Call 执行新人茶馆任务()
    
    // 3. 检查是否开启进阶茶馆
    If 检测进阶茶馆开启() Then
        Call 执行进阶茶馆任务()
        Call 执行材料任务()
    End If
    
    // 4. 完成任务交回
    Call 完成任务交回()
    
    Call 写入日志("茶馆日常任务执行完成")
End Sub

// 接取茶馆任务
Sub 接取茶馆任务()
    Call 写入日志("开始接取茶馆任务")
    
    // 点击赵云睿
    Call 安全点击(赵云睿坐标X, 赵云睿坐标Y, "赵云睿")
    Delay 长延时
    
    // 点击对话选项接取任务
    Call 安全点击(400, 500, "接取任务对话选项")
    Delay 基础延时
    
    // 确认接取任务
    Call 安全点击(450, 550, "确认接取任务")
    Delay 长延时
    
    Call 写入日志("茶馆任务接取完成")
End Sub

// 执行新人茶馆任务
Sub 执行新人茶馆任务()
    Call 写入日志("开始执行新人茶馆任务")
    
    // 任务1: 送茶予客
    Call 执行送茶予客任务()
    
    // 任务2: 动物肉块
    Call 执行动物肉块任务()
    
    // 任务3: 打水煮茶
    Call 执行打水煮茶任务()
    
    // 任务4: 教训混混
    Call 执行教训混混任务()
    
    // 任务5: 采集蜂蜜
    Call 执行采集蜂蜜任务()
    
    // 任务6: 收集柴火
    Call 执行收集柴火任务()
    
    // 任务7: 花茶材料
    Call 执行花茶材料任务()
    
    // 任务8: 客人包裹
    Call 执行客人包裹任务()
    
    // 任务9: 教训毛贼
    Call 执行教训毛贼任务()
    
    // 任务10: 翻晒茶叶
    Call 执行翻晒茶叶任务()
    
    Call 写入日志("新人茶馆任务执行完成")
End Sub

// 检测进阶茶馆是否开启
Function 检测进阶茶馆开启()
    // 检测是否有进阶茶馆的buff或标识
    // 这里需要根据实际游戏界面进行颜色检测
    检测进阶茶馆开启 = True  // 暂时返回True，实际需要根据游戏界面判断
End Function

// 执行进阶茶馆任务
Sub 执行进阶茶馆任务()
    Call 写入日志("开始执行进阶茶馆任务")
    
    // 任务11: 青龙棋局
    Call 执行青龙棋局任务()
    
    // 任务12: 风雅对诗
    Call 执行风雅对诗任务()
    
    // 任务13: 调皮灵猫
    Call 执行调皮灵猫任务()
    
    // 任务14: 江湖秘闻
    Call 执行江湖秘闻任务()
    
    // 任务15: 安顿流民
    Call 执行安顿流民任务()
    
    Call 写入日志("进阶茶馆任务执行完成")
End Sub

// 执行材料任务
Sub 执行材料任务()
    Call 写入日志("开始执行材料任务")
    
    // 肉类材料任务
    Call 执行肉类材料任务()
    
    // 石材材料任务
    Call 执行石材材料任务()
    
    // 药材材料任务
    Call 执行药材材料任务()
    
    // 布料材料任务
    Call 执行布料材料任务()
    
    Call 写入日志("材料任务执行完成")
End Sub

// 完成任务交回
Sub 完成任务交回()
    Call 写入日志("开始完成任务交回")
    
    // 最后与赵云睿对话完成茶馆任务
    Call 安全点击(赵云睿坐标X, 赵云睿坐标Y, "赵云睿")
    Delay 长延时
    
    // 完成茶馆任务
    Call 安全点击(400, 500, "完成茶馆任务")
    Delay 基础延时
    
    // 确认完成
    Call 安全点击(450, 550, "确认完成")
    Delay 长延时
    
    // 检查是否有落难侠士包裹可以拾取
    Call 安全点击(赵云睿坐标X + 20, 赵云睿坐标Y + 50, "落难侠士包裹")
    Delay 基础延时
    
    Call 写入日志("茶馆任务交回完成")
End Sub

// 主程序入口
Sub Main()
    Call 初始化脚本()
    
    If 脚本运行状态 Then
        Call 执行茶馆日常()
    End If
    
    Call 写入日志("脚本执行结束")
End Sub

// 启动脚本
Call Main()
