# 贪吃蛇游戏 Makefile

# 变量定义
BINARY_NAME=snake-game
MAIN_FILE=main.go

# 默认目标
.PHONY: all
all: build

# 安装依赖
.PHONY: deps
deps:
	@echo "正在安装依赖..."
	go mod tidy
	go mod download

# 运行游戏
.PHONY: run
run:
	@echo "正在启动贪吃蛇游戏..."
	go run $(MAIN_FILE)

# 编译游戏
.PHONY: build
build: deps
	@echo "正在编译游戏..."
	go build -o $(BINARY_NAME) $(MAIN_FILE)
	@echo "编译完成: $(BINARY_NAME)"

# 编译并运行
.PHONY: build-run
build-run: build
	@echo "正在运行编译后的游戏..."
	./$(BINARY_NAME)

# 清理编译文件
.PHONY: clean
clean:
	@echo "正在清理编译文件..."
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_NAME).exe
	@echo "清理完成"

# 格式化代码
.PHONY: fmt
fmt:
	@echo "正在格式化代码..."
	go fmt ./...

# 代码检查
.PHONY: vet
vet:
	@echo "正在进行代码检查..."
	go vet ./...

# 运行测试
.PHONY: test
test:
	@echo "正在运行测试..."
	go test ./...

# 交叉编译
.PHONY: build-all
build-all: deps
	@echo "正在进行交叉编译..."
	# Windows 64位
	GOOS=windows GOARCH=amd64 go build -o $(BINARY_NAME)-windows-amd64.exe $(MAIN_FILE)
	# Linux 64位
	GOOS=linux GOARCH=amd64 go build -o $(BINARY_NAME)-linux-amd64 $(MAIN_FILE)
	# macOS 64位
	GOOS=darwin GOARCH=amd64 go build -o $(BINARY_NAME)-darwin-amd64 $(MAIN_FILE)
	# macOS ARM64 (M1/M2)
	GOOS=darwin GOARCH=arm64 go build -o $(BINARY_NAME)-darwin-arm64 $(MAIN_FILE)
	@echo "交叉编译完成"

# 帮助信息
.PHONY: help
help:
	@echo "贪吃蛇游戏 - 可用命令:"
	@echo ""
	@echo "  make run        - 直接运行游戏"
	@echo "  make build      - 编译游戏"
	@echo "  make build-run  - 编译并运行游戏"
	@echo "  make deps       - 安装依赖"
	@echo "  make clean      - 清理编译文件"
	@echo "  make fmt        - 格式化代码"
	@echo "  make vet        - 代码检查"
	@echo "  make test       - 运行测试"
	@echo "  make build-all  - 交叉编译所有平台"
	@echo "  make help       - 显示此帮助信息"
	@echo ""
	@echo "快速开始:"
	@echo "  make run        # 直接运行游戏"
	@echo "  make build-run  # 编译后运行"
