package main

import (
	"bufio"
	"fmt"
	"math/rand"
	"os"
	"strings"
	"time"
)

const (
	WIDTH  = 15
	HEIGHT = 10
)

type Point struct {
	X, Y int
}

type Game struct {
	snake     []Point
	food      Point
	direction string
	gameOver  bool
	score     int
}

func NewGame() *Game {
	return &Game{
		snake:     []Point{{WIDTH / 2, HEIGHT / 2}},
		direction: "RIGHT",
		gameOver:  false,
		score:     0,
	}
}

func (g *Game) generateFood() {
	for {
		g.food = Point{
			X: rand.Intn(WIDTH),
			Y: rand.Intn(HEIGHT),
		}
		
		onSnake := false
		for _, segment := range g.snake {
			if segment.X == g.food.X && segment.Y == g.food.Y {
				onSnake = true
				break
			}
		}
		
		if !onSnake {
			break
		}
	}
}

func (g *Game) move() {
	if g.gameOver {
		return
	}
	
	head := g.snake[0]
	var newHead Point
	
	switch g.direction {
	case "UP":
		newHead = Point{head.X, head.Y - 1}
	case "DOWN":
		newHead = Point{head.X, head.Y + 1}
	case "LEFT":
		newHead = Point{head.X - 1, head.Y}
	case "RIGHT":
		newHead = Point{head.X + 1, head.Y}
	}
	
	// 检查边界
	if newHead.X < 0 || newHead.X >= WIDTH || newHead.Y < 0 || newHead.Y >= HEIGHT {
		g.gameOver = true
		return
	}
	
	// 检查自身碰撞
	for _, segment := range g.snake {
		if newHead.X == segment.X && newHead.Y == segment.Y {
			g.gameOver = true
			return
		}
	}
	
	g.snake = append([]Point{newHead}, g.snake...)
	
	// 检查食物
	if newHead.X == g.food.X && newHead.Y == g.food.Y {
		g.score += 10
		g.generateFood()
	} else {
		g.snake = g.snake[:len(g.snake)-1]
	}
}

func (g *Game) render() {
	// 创建游戏板
	board := make([][]string, HEIGHT)
	for i := range board {
		board[i] = make([]string, WIDTH)
		for j := range board[i] {
			board[i][j] = " "
		}
	}
	
	// 放置食物
	board[g.food.Y][g.food.X] = "*"
	
	// 放置蛇
	for i, segment := range g.snake {
		if i == 0 {
			board[segment.Y][segment.X] = "O" // 蛇头
		} else {
			board[segment.Y][segment.X] = "o" // 蛇身
		}
	}
	
	// 清屏并显示
	fmt.Print("\033[2J\033[H") // ANSI清屏
	
	// 绘制顶部边框
	fmt.Print("+")
	for i := 0; i < WIDTH; i++ {
		fmt.Print("-")
	}
	fmt.Println("+")
	
	// 绘制游戏区域
	for y := 0; y < HEIGHT; y++ {
		fmt.Print("|")
		for x := 0; x < WIDTH; x++ {
			fmt.Print(board[y][x])
		}
		fmt.Println("|")
	}
	
	// 绘制底部边框
	fmt.Print("+")
	for i := 0; i < WIDTH; i++ {
		fmt.Print("-")
	}
	fmt.Println("+")
	
	fmt.Printf("分数: %d\n", g.score)
	if g.gameOver {
		fmt.Println("游戏结束! 输入 'r' 重新开始，'q' 退出")
	} else {
		fmt.Println("输入方向 (w/a/s/d) 然后按回车，'q' 退出")
	}
}

func (g *Game) restart() {
	g.snake = []Point{{WIDTH / 2, HEIGHT / 2}}
	g.direction = "RIGHT"
	g.gameOver = false
	g.score = 0
	g.generateFood()
}

func main() {
	rand.Seed(time.Now().UnixNano())
	
	game := NewGame()
	game.generateFood()
	
	reader := bufio.NewReader(os.Stdin)
	
	fmt.Println("=== 简单贪吃蛇游戏 ===")
	fmt.Println("使用 w(上) a(左) s(下) d(右) 控制")
	fmt.Println("输入命令后按回车确认")
	fmt.Println("按回车开始...")
	reader.ReadLine()
	
	// 游戏主循环
	go func() {
		for {
			if !game.gameOver {
				game.move()
			}
			game.render()
			time.Sleep(800 * time.Millisecond)
		}
	}()
	
	// 输入处理
	for {
		input, _ := reader.ReadString('\n')
		input = strings.TrimSpace(strings.ToLower(input))
		
		switch input {
		case "w":
			if game.direction != "DOWN" {
				game.direction = "UP"
			}
		case "s":
			if game.direction != "UP" {
				game.direction = "DOWN"
			}
		case "a":
			if game.direction != "RIGHT" {
				game.direction = "LEFT"
			}
		case "d":
			if game.direction != "LEFT" {
				game.direction = "RIGHT"
			}
		case "r":
			game.restart()
		case "q":
			fmt.Println("谢谢游戏!")
			return
		}
	}
}
