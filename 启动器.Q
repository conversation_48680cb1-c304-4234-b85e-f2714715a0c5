//==============================================================================
// 剑网三缘起茶馆日常脚本启动器
// 版本: 1.0
// 功能: 提供简单的图形界面来配置和启动茶馆日常脚本
//==============================================================================

// 全局变量
Dim 脚本状态, 配置已加载, 游戏窗口句柄

// 初始化启动器
Sub 初始化启动器()
    脚本状态 = 0  // 0=停止, 1=运行中, 2=暂停
    配置已加载 = False
    
    // 显示启动器界面
    Call 显示启动器界面()
    
    // 加载配置
    Call 加载配置文件()
End Sub

// 显示启动器界面
Sub 显示启动器界面()
    MessageBox "剑网三茶馆日常脚本启动器" & vbCrLf & vbCrLf & _
              "功能菜单：" & vbCrLf & _
              "1. 开始执行脚本" & vbCrLf & _
              "2. 配置设置" & vbCrLf & _
              "3. 坐标获取工具" & vbCrLf & _
              "4. 测试游戏连接" & vbCrLf & _
              "5. 查看日志" & vbCrLf & _
              "6. 退出程序" & vbCrLf & vbCrLf & _
              "请选择功能 (1-6):"
    
    Dim 用户选择
    用户选择 = InputBox("请输入选项 (1-6):", "功能选择", "1")
    
    Select Case 用户选择
        Case "1"
            Call 开始执行脚本()
        Case "2"
            Call 打开配置设置()
        Case "3"
            Call 坐标获取工具()
        Case "4"
            Call 测试游戏连接()
        Case "5"
            Call 查看日志文件()
        Case "6"
            Exit Sub
        Case Else
            MessageBox "无效选项，程序退出"
    End Select
End Sub

// 加载配置文件
Sub 加载配置文件()
    Dim fso
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    If fso.FileExists("config.txt") Then
        配置已加载 = True
        MessageBox "配置文件加载成功"
    Else
        MessageBox "配置文件 config.txt 不存在，请先运行快速设置向导"
        配置已加载 = False
    End If
End Sub

// 检测游戏状态
Function 检测游戏状态()
    游戏窗口句柄 = FindWindow("", "剑网3")
    If 游戏窗口句柄 = 0 Then
        游戏窗口句柄 = FindWindow("", "JX3")
    End If
    
    If 游戏窗口句柄 > 0 Then
        检测游戏状态 = True
    Else
        检测游戏状态 = False
    End If
End Function

// 开始执行脚本
Sub 开始执行脚本()
    If Not 检测游戏状态() Then
        MessageBox "未找到游戏窗口，请先启动剑网三游戏"
        Exit Sub
    End If
    
    If Not 配置已加载 Then
        MessageBox "配置文件未加载，请先运行快速设置向导"
        Exit Sub
    End If
    
    Dim 确认执行
    确认执行 = MessageBox("确定要开始执行茶馆日常脚本吗？" & vbCrLf & vbCrLf & _
                        "请确保：" & vbCrLf & _
                        "• 角色位于茶馆区域" & vbCrLf & _
                        "• 游戏界面清晰可见" & vbCrLf & _
                        "• 没有其他窗口遮挡", vbYesNo)
    
    If 确认执行 = vbYes Then
        脚本状态 = 1
        MessageBox "脚本开始执行，请不要操作鼠标键盘"
        
        // 激活游戏窗口
        Call Plugin.Window.Active(游戏窗口句柄)
        Delay 1000
        
        // 执行主脚本
        RunScript "主脚本.Q"
    End If
End Sub

// 打开配置设置
Sub 打开配置设置()
    Dim 配置选项
    配置选项 = MessageBox("配置设置选项：" & vbCrLf & vbCrLf & _
                         "1. 修改NPC坐标" & vbCrLf & _
                         "2. 调整延时设置" & vbCrLf & _
                         "3. 重置为默认配置" & vbCrLf & _
                         "4. 返回主菜单" & vbCrLf & vbCrLf & _
                         "请选择 (1-4):")
    
    Dim 用户选择
    用户选择 = InputBox("请输入选项 (1-4):", "配置设置", "1")
    
    Select Case 用户选择
        Case "1"
            Call 修改NPC坐标()
        Case "2"
            Call 调整延时设置()
        Case "3"
            Call 重置默认配置()
        Case "4"
            Call 显示启动器界面()
    End Select
End Sub

// 修改NPC坐标
Sub 修改NPC坐标()
    MessageBox "NPC坐标配置" & vbCrLf & vbCrLf & _
              "请按以下步骤操作：" & vbCrLf & _
              "1. 将鼠标移动到赵云睿身上" & vbCrLf & _
              "2. 按 F1 键记录坐标" & vbCrLf & _
              "3. 将鼠标移动到茶馆小童身上" & vbCrLf & _
              "4. 按 F2 键记录坐标"
    
    Dim 赵云睿X, 赵云睿Y, 茶馆小童X, 茶馆小童Y
    
    // 等待F1键获取赵云睿坐标
    MessageBox "请将鼠标移动到赵云睿身上，然后按F1键"
    Do
        If GetAsyncKeyState(112) Then  // F1键
            GetCursorPos 赵云睿X, 赵云睿Y
            MessageBox "赵云睿坐标已记录: (" & 赵云睿X & "," & 赵云睿Y & ")"
            Exit Do
        End If
        Delay 100
    Loop
    
    // 等待F2键获取茶馆小童坐标
    MessageBox "请将鼠标移动到茶馆小童身上，然后按F2键"
    Do
        If GetAsyncKeyState(113) Then  // F2键
            GetCursorPos 茶馆小童X, 茶馆小童Y
            MessageBox "茶馆小童坐标已记录: (" & 茶馆小童X & "," & 茶馆小童Y & ")"
            Exit Do
        End If
        Delay 100
    Loop
    
    // 保存坐标到配置文件
    WriteIni "config.txt", "坐标", "赵云睿X", 赵云睿X
    WriteIni "config.txt", "坐标", "赵云睿Y", 赵云睿Y
    WriteIni "config.txt", "坐标", "茶馆小童X", 茶馆小童X
    WriteIni "config.txt", "坐标", "茶馆小童Y", 茶馆小童Y
    
    MessageBox "坐标配置已保存"
End Sub

// 调整延时设置
Sub 调整延时设置()
    Dim 当前基础延时, 新基础延时
    当前基础延时 = ReadIni("config.txt", "延时", "基础延时", "1000")
    
    新基础延时 = InputBox("当前基础延时: " & 当前基础延时 & "ms" & vbCrLf & vbCrLf & _
                        "建议设置：" & vbCrLf & _
                        "• 网络良好: 1000ms" & vbCrLf & _
                        "• 网络一般: 1500ms" & vbCrLf & _
                        "• 网络较慢: 2000ms" & vbCrLf & vbCrLf & _
                        "请输入新的基础延时(ms):", "延时设置", 当前基础延时)
    
    If IsNumeric(新基础延时) And 新基础延时 >= 500 Then
        WriteIni "config.txt", "延时", "基础延时", 新基础延时
        WriteIni "config.txt", "延时", "长延时", 新基础延时 * 3
        WriteIni "config.txt", "延时", "短延时", 新基础延时 / 2
        MessageBox "延时设置已更新"
    Else
        MessageBox "无效的延时值，请输入大于500的数字"
    End If
End Sub

// 重置默认配置
Sub 重置默认配置()
    Dim 确认重置
    确认重置 = MessageBox("确定要重置为默认配置吗？" & vbCrLf & "这将覆盖所有当前设置", vbYesNo)
    
    If 确认重置 = vbYes Then
        // 重置坐标
        WriteIni "config.txt", "坐标", "赵云睿X", "500"
        WriteIni "config.txt", "坐标", "赵云睿Y", "300"
        WriteIni "config.txt", "坐标", "茶馆小童X", "520"
        WriteIni "config.txt", "坐标", "茶馆小童Y", "320"
        
        // 重置延时
        WriteIni "config.txt", "延时", "基础延时", "1000"
        WriteIni "config.txt", "延时", "长延时", "3000"
        WriteIni "config.txt", "延时", "短延时", "500"
        
        MessageBox "配置已重置为默认值"
    End If
End Sub

// 坐标获取工具
Sub 坐标获取工具()
    MessageBox "坐标获取工具" & vbCrLf & vbCrLf & _
              "操作说明：" & vbCrLf & _
              "• 将鼠标移动到目标位置" & vbCrLf & _
              "• 按 F1 键获取坐标" & vbCrLf & _
              "• 按 ESC 键退出工具"
    
    Do
        If GetAsyncKeyState(112) Then  // F1键
            Dim 鼠标X, 鼠标Y
            GetCursorPos 鼠标X, 鼠标Y
            MessageBox "当前坐标: X=" & 鼠标X & ", Y=" & 鼠标Y
        End If
        
        If GetAsyncKeyState(27) Then  // ESC键
            Exit Do
        End If
        
        Delay 100
    Loop
End Sub

// 测试游戏连接
Sub 测试游戏连接()
    If 检测游戏状态() Then
        MessageBox "✅ 游戏连接正常" & vbCrLf & _
                  "游戏窗口句柄: " & 游戏窗口句柄
    Else
        MessageBox "❌ 未找到游戏窗口" & vbCrLf & vbCrLf & _
                  "请检查：" & vbCrLf & _
                  "• 剑网三游戏是否已启动" & vbCrLf & _
                  "• 游戏窗口标题是否正确" & vbCrLf & _
                  "• 游戏是否处于前台显示"
    End If
End Sub

// 查看日志文件
Sub 查看日志文件()
    Dim fso
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    If fso.FileExists("jx3_log.txt") Then
        Dim 打开方式
        打开方式 = MessageBox("选择查看方式：" & vbCrLf & vbCrLf & _
                             "是(Y) - 用记事本打开" & vbCrLf & _
                             "否(N) - 显示最后几行", vbYesNo)
        
        If 打开方式 = vbYes Then
            // 用记事本打开
            CreateObject("WScript.Shell").Run "notepad.exe jx3_log.txt"
        Else
            // 显示最后几行
            Dim 文件对象, 日志内容
            Set 文件对象 = fso.OpenTextFile("jx3_log.txt", 1)
            日志内容 = 文件对象.ReadAll()
            文件对象.Close
            
            // 显示最后500个字符
            If Len(日志内容) > 500 Then
                日志内容 = "..." & Right(日志内容, 500)
            End If
            
            MessageBox "最近的日志内容：" & vbCrLf & vbCrLf & 日志内容
        End If
    Else
        MessageBox "日志文件不存在，请先运行脚本"
    End If
End Sub

// 主程序入口
Sub Main()
    Call 初始化启动器()
End Sub

// 启动启动器
Call Main()
