@echo off
chcp 65001 >nul
echo ========================================
echo 贪吃蛇游戏编译脚本
echo ========================================
echo.

:: 检查Go是否已安装
echo [1/4] 检查Go语言环境...
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Go语言环境！
    echo.
    echo 请先安装Go语言：
    echo 1. 访问 https://golang.org/dl/
    echo 2. 下载并安装最新版本的Go
    echo 3. 重新运行此脚本
    echo.
    echo 或者查看 INSTALL_GUIDE.md 获取详细安装指南
    pause
    exit /b 1
)

for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i
echo ✅ Go语言环境已安装: %GO_VERSION%
echo.

:: 设置Go代理（提高下载速度）
echo [2/4] 配置Go代理...
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=sum.golang.google.cn
echo ✅ Go代理配置完成
echo.

:: 安装依赖
echo [3/4] 安装项目依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败！
    echo 请检查网络连接或Go环境配置
    pause
    exit /b 1
)
echo ✅ 依赖安装完成
echo.

:: 编译游戏
echo [4/4] 编译贪吃蛇游戏...
go build -ldflags="-s -w" -o snake-game.exe main.go
if %errorlevel% neq 0 (
    echo ❌ 编译失败！
    echo 请检查代码或Go环境
    pause
    exit /b 1
)
echo ✅ 编译成功！
echo.

:: 检查编译结果
if exist snake-game.exe (
    for %%A in (snake-game.exe) do set FILE_SIZE=%%~zA
    echo 📦 生成文件: snake-game.exe
    echo 📏 文件大小: %FILE_SIZE% 字节
    echo.
    
    echo ========================================
    echo 🎉 编译完成！
    echo ========================================
    echo.
    echo 游戏已成功编译为: snake-game.exe
    echo.
    echo 运行方式：
    echo 1. 双击 snake-game.exe 直接运行
    echo 2. 或在命令行中输入: snake-game.exe
    echo.
    echo 游戏操作：
    echo • 方向键 ↑↓←→ - 控制蛇的移动
    echo • 空格键 - 重新开始游戏
    echo • 点击"开始游戏"按钮 - 重启游戏
    echo.
    
    set /p RUN_GAME="是否立即运行游戏？(Y/N): "
    if /i "%RUN_GAME%"=="Y" (
        echo.
        echo 🚀 正在启动游戏...
        start snake-game.exe
    )
) else (
    echo ❌ 编译失败：未找到生成的可执行文件
)

echo.
echo 按任意键退出...
pause >nul
