package main

import (
	"fmt"
	"image/color"
	"math/rand"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

const (
	GRID_WIDTH  = 20
	GRID_HEIGHT = 20
	CELL_SIZE   = 20
)

type Direction int

const (
	UP Direction = iota
	DOWN
	LEFT
	RIGHT
)

type Point struct {
	X, Y int
}

type Game struct {
	snake     []Point
	food      Point
	direction Direction
	gameOver  bool
	score     int
	canvas    *fyne.Container
	cells     [][]*canvas.Rectangle
	scoreText *widget.Label
	gameText  *widget.Label
}

func NewGame() *Game {
	game := &Game{
		snake:     []Point{{10, 10}},
		direction: RIGHT,
		gameOver:  false,
		score:     0,
	}
	
	// 初始化食物位置
	game.generateFood()
	
	return game
}

func (g *Game) generateFood() {
	for {
		g.food = Point{
			X: rand.Intn(GRID_WIDTH),
			Y: rand.Intn(GRID_HEIGHT),
		}
		
		// 确保食物不在蛇身上
		onSnake := false
		for _, segment := range g.snake {
			if segment.X == g.food.X && segment.Y == g.food.Y {
				onSnake = true
				break
			}
		}
		
		if !onSnake {
			break
		}
	}
}

func (g *Game) createUI() *fyne.Container {
	// 创建网格
	g.cells = make([][]*canvas.Rectangle, GRID_HEIGHT)
	gridContainer := container.NewWithoutLayout()
	
	for y := 0; y < GRID_HEIGHT; y++ {
		g.cells[y] = make([]*canvas.Rectangle, GRID_WIDTH)
		for x := 0; x < GRID_WIDTH; x++ {
			cell := canvas.NewRectangle(color.RGBA{50, 50, 50, 255})
			cell.Resize(fyne.NewSize(CELL_SIZE, CELL_SIZE))
			cell.Move(fyne.NewPos(float32(x*CELL_SIZE), float32(y*CELL_SIZE)))
			cell.StrokeColor = color.RGBA{100, 100, 100, 255}
			cell.StrokeWidth = 1
			
			g.cells[y][x] = cell
			gridContainer.Add(cell)
		}
	}
	
	// 创建分数显示
	g.scoreText = widget.NewLabel("分数: 0")
	g.scoreText.TextStyle = fyne.TextStyle{Bold: true}
	
	// 创建游戏状态显示
	g.gameText = widget.NewLabel("使用方向键控制蛇的移动")
	
	// 创建控制按钮
	startBtn := widget.NewButton("开始游戏", func() {
		g.restart()
	})
	
	pauseBtn := widget.NewButton("暂停/继续", func() {
		// 这里可以添加暂停逻辑
	})
	
	// 创建说明文本
	instructions := widget.NewLabel(
		"游戏说明:\n" +
		"• 使用方向键控制蛇的移动\n" +
		"• 吃到红色食物可以增长并得分\n" +
		"• 撞到墙壁或自己身体游戏结束\n" +
		"• 按开始游戏重新开始")
	instructions.Wrapping = fyne.TextWrapWord
	
	// 布局
	infoContainer := container.NewVBox(
		g.scoreText,
		g.gameText,
		widget.NewSeparator(),
		container.NewHBox(startBtn, pauseBtn),
		widget.NewSeparator(),
		instructions,
	)
	
	mainContainer := container.NewHBox(
		container.NewWithoutLayout(gridContainer),
		widget.NewSeparator(),
		infoContainer,
	)
	
	g.canvas = gridContainer
	g.updateDisplay()
	
	return mainContainer
}

func (g *Game) updateDisplay() {
	// 清空所有格子
	for y := 0; y < GRID_HEIGHT; y++ {
		for x := 0; x < GRID_WIDTH; x++ {
			g.cells[y][x].FillColor = color.RGBA{50, 50, 50, 255}
		}
	}
	
	// 绘制蛇
	for i, segment := range g.snake {
		if segment.X >= 0 && segment.X < GRID_WIDTH && 
		   segment.Y >= 0 && segment.Y < GRID_HEIGHT {
			if i == 0 {
				// 蛇头 - 亮绿色
				g.cells[segment.Y][segment.X].FillColor = color.RGBA{0, 255, 0, 255}
			} else {
				// 蛇身 - 深绿色
				g.cells[segment.Y][segment.X].FillColor = color.RGBA{0, 150, 0, 255}
			}
		}
	}
	
	// 绘制食物
	if g.food.X >= 0 && g.food.X < GRID_WIDTH && 
	   g.food.Y >= 0 && g.food.Y < GRID_HEIGHT {
		g.cells[g.food.Y][g.food.X].FillColor = color.RGBA{255, 0, 0, 255}
	}
	
	// 更新分数
	g.scoreText.SetText(fmt.Sprintf("分数: %d", g.score))
	
	// 更新游戏状态
	if g.gameOver {
		g.gameText.SetText("游戏结束! 点击开始游戏重新开始")
	} else {
		g.gameText.SetText("游戏进行中...")
	}
	
	// 刷新显示
	for y := 0; y < GRID_HEIGHT; y++ {
		for x := 0; x < GRID_WIDTH; x++ {
			g.cells[y][x].Refresh()
		}
	}
	g.scoreText.Refresh()
	g.gameText.Refresh()
}

func (g *Game) move() {
	if g.gameOver {
		return
	}
	
	head := g.snake[0]
	var newHead Point
	
	switch g.direction {
	case UP:
		newHead = Point{head.X, head.Y - 1}
	case DOWN:
		newHead = Point{head.X, head.Y + 1}
	case LEFT:
		newHead = Point{head.X - 1, head.Y}
	case RIGHT:
		newHead = Point{head.X + 1, head.Y}
	}
	
	// 检查碰撞
	if newHead.X < 0 || newHead.X >= GRID_WIDTH || 
	   newHead.Y < 0 || newHead.Y >= GRID_HEIGHT {
		g.gameOver = true
		return
	}
	
	// 检查是否撞到自己
	for _, segment := range g.snake {
		if newHead.X == segment.X && newHead.Y == segment.Y {
			g.gameOver = true
			return
		}
	}
	
	// 移动蛇
	g.snake = append([]Point{newHead}, g.snake...)
	
	// 检查是否吃到食物
	if newHead.X == g.food.X && newHead.Y == g.food.Y {
		g.score += 10
		g.generateFood()
	} else {
		// 如果没吃到食物，移除尾部
		g.snake = g.snake[:len(g.snake)-1]
	}
}

func (g *Game) changeDirection(newDirection Direction) {
	// 防止反向移动
	switch {
	case g.direction == UP && newDirection == DOWN:
		return
	case g.direction == DOWN && newDirection == UP:
		return
	case g.direction == LEFT && newDirection == RIGHT:
		return
	case g.direction == RIGHT && newDirection == LEFT:
		return
	}
	
	g.direction = newDirection
}

func (g *Game) restart() {
	g.snake = []Point{{10, 10}}
	g.direction = RIGHT
	g.gameOver = false
	g.score = 0
	g.generateFood()
	g.updateDisplay()
}

func main() {
	rand.Seed(time.Now().UnixNano())
	
	myApp := app.New()
	myApp.SetIcon(nil)
	myWindow := myApp.NewWindow("贪吃蛇游戏")
	myWindow.Resize(fyne.NewSize(800, 600))
	myWindow.CenterOnScreen()
	
	game := NewGame()
	content := game.createUI()
	
	myWindow.SetContent(content)
	
	// 键盘事件处理
	myWindow.Canvas().SetOnTypedKey(func(key *fyne.KeyEvent) {
		switch key.Name {
		case fyne.KeyUp:
			game.changeDirection(UP)
		case fyne.KeyDown:
			game.changeDirection(DOWN)
		case fyne.KeyLeft:
			game.changeDirection(LEFT)
		case fyne.KeyRight:
			game.changeDirection(RIGHT)
		case fyne.KeySpace:
			game.restart()
		}
	})
	
	// 游戏循环
	go func() {
		ticker := time.NewTicker(200 * time.Millisecond)
		defer ticker.Stop()
		
		for range ticker.C {
			game.move()
			game.updateDisplay()
		}
	}()
	
	myWindow.ShowAndRun()
}
