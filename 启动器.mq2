//==============================================================================
// 剑网三缘起茶馆日常脚本启动器
// 版本: 1.0
// 功能: 提供简单的图形界面来配置和启动茶馆日常脚本
//==============================================================================

// 全局变量
Dim 主窗口句柄, 脚本状态, 配置已加载

// 初始化启动器
Sub 初始化启动器()
    脚本状态 = 0  // 0=停止, 1=运行中, 2=暂停
    配置已加载 = False
    
    // 创建主界面
    Call 创建主界面()
    
    // 加载配置
    Call 加载配置文件()
End Sub

// 创建主界面
Sub 创建主界面()
    // 创建窗口
    主窗口句柄 = CreateWindow("剑网三茶馆日常脚本", 300, 400)
    
    // 添加控件
    Call AddButton(主窗口句柄, "开始执行", 50, 50, 100, 30, 1001)
    Call AddButton(主窗口句柄, "停止执行", 170, 50, 100, 30, 1002)
    Call AddButton(主窗口句柄, "暂停/继续", 50, 90, 100, 30, 1003)
    Call AddButton(主窗口句柄, "配置设置", 170, 90, 100, 30, 1004)
    
    Call AddButton(主窗口句柄, "坐标获取", 50, 130, 100, 30, 1005)
    Call AddButton(主窗口句柄, "颜色获取", 170, 130, 100, 30, 1006)
    Call AddButton(主窗口句柄, "测试连接", 50, 170, 100, 30, 1007)
    Call AddButton(主窗口句柄, "查看日志", 170, 170, 100, 30, 1008)
    
    // 添加状态显示
    Call AddLabel(主窗口句柄, "脚本状态: 已停止", 50, 220, 200, 20, 2001)
    Call AddLabel(主窗口句柄, "游戏状态: 未检测", 50, 250, 200, 20, 2002)
    Call AddLabel(主窗口句柄, "配置状态: 未加载", 50, 280, 200, 20, 2003)
    
    // 添加进度条
    Call AddProgressBar(主窗口句柄, 50, 320, 200, 20, 3001)
    
    // 显示窗口
    ShowWindow 主窗口句柄
End Sub

// 加载配置文件
Sub 加载配置文件()
    If 文件是否存在("config.txt") Then
        配置已加载 = True
        Call UpdateLabel(主窗口句柄, 2003, "配置状态: 已加载")
    Else
        Call UpdateLabel(主窗口句柄, 2003, "配置状态: 配置文件不存在")
        MessageBox "配置文件 config.txt 不存在，请先创建配置文件"
    End If
End Sub

// 检测游戏状态
Function 检测游戏状态()
    Dim 游戏窗口
    游戏窗口 = FindWindow("", "剑网3")
    If 游戏窗口 = 0 Then
        游戏窗口 = FindWindow("", "JX3")
    End If
    
    If 游戏窗口 > 0 Then
        Call UpdateLabel(主窗口句柄, 2002, "游戏状态: 已连接")
        检测游戏状态 = True
    Else
        Call UpdateLabel(主窗口句柄, 2002, "游戏状态: 未找到游戏")
        检测游戏状态 = False
    End If
End Function

// 按钮事件处理
Sub 处理按钮事件(按钮ID)
    Select Case 按钮ID
        Case 1001  // 开始执行
            Call 开始执行脚本()
        Case 1002  // 停止执行
            Call 停止执行脚本()
        Case 1003  // 暂停/继续
            Call 暂停继续脚本()
        Case 1004  // 配置设置
            Call 打开配置设置()
        Case 1005  // 坐标获取
            Call 坐标获取工具()
        Case 1006  // 颜色获取
            Call 颜色获取工具()
        Case 1007  // 测试连接
            Call 测试游戏连接()
        Case 1008  // 查看日志
            Call 查看日志文件()
    End Select
End Sub

// 开始执行脚本
Sub 开始执行脚本()
    If Not 检测游戏状态() Then
        MessageBox "请先启动剑网三游戏"
        Exit Sub
    End If
    
    If Not 配置已加载 Then
        MessageBox "请先加载配置文件"
        Exit Sub
    End If
    
    脚本状态 = 1
    Call UpdateLabel(主窗口句柄, 2001, "脚本状态: 运行中")
    
    // 启动主脚本
    RunApp "jx3_chaguan_daily.mq2"
End Sub

// 停止执行脚本
Sub 停止执行脚本()
    脚本状态 = 0
    Call UpdateLabel(主窗口句柄, 2001, "脚本状态: 已停止")
    
    // 这里可以添加停止脚本的逻辑
    MessageBox "脚本已停止"
End Sub

// 暂停/继续脚本
Sub 暂停继续脚本()
    If 脚本状态 = 1 Then
        脚本状态 = 2
        Call UpdateLabel(主窗口句柄, 2001, "脚本状态: 已暂停")
    ElseIf 脚本状态 = 2 Then
        脚本状态 = 1
        Call UpdateLabel(主窗口句柄, 2001, "脚本状态: 运行中")
    End If
End Sub

// 打开配置设置
Sub 打开配置设置()
    // 创建配置窗口
    Dim 配置窗口
    配置窗口 = CreateWindow("配置设置", 400, 500)
    
    // 添加配置选项
    Call AddLabel(配置窗口, "赵云睿坐标:", 20, 30, 100, 20, 4001)
    Call AddEdit(配置窗口, "500", 130, 30, 60, 20, 5001)
    Call AddEdit(配置窗口, "300", 200, 30, 60, 20, 5002)
    
    Call AddLabel(配置窗口, "茶馆小童坐标:", 20, 60, 100, 20, 4002)
    Call AddEdit(配置窗口, "520", 130, 60, 60, 20, 5003)
    Call AddEdit(配置窗口, "320", 200, 60, 60, 20, 5004)
    
    Call AddLabel(配置窗口, "基础延时(ms):", 20, 90, 100, 20, 4003)
    Call AddEdit(配置窗口, "1000", 130, 90, 100, 20, 5005)
    
    Call AddLabel(配置窗口, "长延时(ms):", 20, 120, 100, 20, 4004)
    Call AddEdit(配置窗口, "3000", 130, 120, 100, 20, 5006)
    
    Call AddButton(配置窗口, "保存配置", 50, 200, 80, 30, 6001)
    Call AddButton(配置窗口, "取消", 150, 200, 80, 30, 6002)
    Call AddButton(配置窗口, "恢复默认", 250, 200, 80, 30, 6003)
    
    ShowWindow 配置窗口
End Sub

// 坐标获取工具
Sub 坐标获取工具()
    MessageBox "请将鼠标移动到目标位置，然后按F1键获取坐标"
    
    // 等待F1键按下
    Do
        If GetAsyncKeyState(112) Then  // F1键
            Dim 鼠标X, 鼠标Y
            GetCursorPos 鼠标X, 鼠标Y
            MessageBox "当前坐标: X=" & 鼠标X & ", Y=" & 鼠标Y
            Exit Do
        End If
        Delay 100
    Loop
End Sub

// 颜色获取工具
Sub 颜色获取工具()
    MessageBox "请将鼠标移动到目标位置，然后按F2键获取颜色"
    
    // 等待F2键按下
    Do
        If GetAsyncKeyState(113) Then  // F2键
            Dim 鼠标X, 鼠标Y, 颜色值
            GetCursorPos 鼠标X, 鼠标Y
            颜色值 = GetPixelColor(鼠标X, 鼠标Y)
            MessageBox "坐标(" & 鼠标X & "," & 鼠标Y & ")的颜色值: " & Hex(颜色值)
            Exit Do
        End If
        Delay 100
    Loop
End Sub

// 测试游戏连接
Sub 测试游戏连接()
    If 检测游戏状态() Then
        MessageBox "游戏连接正常"
    Else
        MessageBox "未找到游戏窗口，请确保游戏已启动"
    End If
End Sub

// 查看日志文件
Sub 查看日志文件()
    If 文件是否存在("jx3_log.txt") Then
        RunApp "notepad.exe jx3_log.txt"
    Else
        MessageBox "日志文件不存在"
    End If
End Sub

// 文件是否存在检查
Function 文件是否存在(文件路径)
    Dim fso
    Set fso = CreateObject("Scripting.FileSystemObject")
    文件是否存在 = fso.FileExists(文件路径)
End Function

// 主程序入口
Sub Main()
    Call 初始化启动器()
    
    // 消息循环
    Do
        Delay 100
        // 这里可以添加消息处理逻辑
    Loop While True
End Sub

// 启动启动器
Call Main()
