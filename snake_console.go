package main

import (
	"fmt"
	"math/rand"
	"os"
	"os/exec"
	"runtime"
	"time"
)

const (
	WIDTH  = 20
	HEIGHT = 10
)

type Direction int

const (
	UP Direction = iota
	DOWN
	LEFT
	RIGHT
)

type Point struct {
	X, Y int
}

type Game struct {
	snake     []Point
	food      Point
	direction Direction
	gameOver  bool
	score     int
	board     [][]rune
}

func NewGame() *Game {
	game := &Game{
		snake:     []Point{{WIDTH / 2, HEIGHT / 2}},
		direction: RIGHT,
		gameOver:  false,
		score:     0,
		board:     make([][]rune, HEIGHT),
	}
	
	for i := range game.board {
		game.board[i] = make([]rune, WIDTH)
	}
	
	game.generateFood()
	return game
}

func (g *Game) generateFood() {
	for {
		g.food = Point{
			X: rand.Intn(WIDTH),
			Y: rand.Intn(HEIGHT),
		}
		
		onSnake := false
		for _, segment := range g.snake {
			if segment.X == g.food.X && segment.Y == g.food.Y {
				onSnake = true
				break
			}
		}
		
		if !onSnake {
			break
		}
	}
}

func (g *Game) move() {
	if g.gameOver {
		return
	}
	
	head := g.snake[0]
	var newHead Point
	
	switch g.direction {
	case UP:
		newHead = Point{head.X, head.Y - 1}
	case DOWN:
		newHead = Point{head.X, head.Y + 1}
	case LEFT:
		newHead = Point{head.X - 1, head.Y}
	case RIGHT:
		newHead = Point{head.X + 1, head.Y}
	}
	
	// 检查边界碰撞
	if newHead.X < 0 || newHead.X >= WIDTH || newHead.Y < 0 || newHead.Y >= HEIGHT {
		g.gameOver = true
		return
	}
	
	// 检查自身碰撞
	for _, segment := range g.snake {
		if newHead.X == segment.X && newHead.Y == segment.Y {
			g.gameOver = true
			return
		}
	}
	
	g.snake = append([]Point{newHead}, g.snake...)
	
	// 检查食物
	if newHead.X == g.food.X && newHead.Y == g.food.Y {
		g.score += 10
		g.generateFood()
	} else {
		g.snake = g.snake[:len(g.snake)-1]
	}
}

func (g *Game) render() {
	clearScreen()
	
	// 清空棋盘
	for y := 0; y < HEIGHT; y++ {
		for x := 0; x < WIDTH; x++ {
			g.board[y][x] = ' '
		}
	}
	
	// 放置食物
	g.board[g.food.Y][g.food.X] = '*'
	
	// 放置蛇
	for i, segment := range g.snake {
		if i == 0 {
			g.board[segment.Y][segment.X] = 'O' // 蛇头
		} else {
			g.board[segment.Y][segment.X] = 'o' // 蛇身
		}
	}
	
	// 绘制边框和内容
	fmt.Println("+" + string(make([]rune, WIDTH*2)) + "+")
	for y := 0; y < HEIGHT; y++ {
		fmt.Print("|")
		for x := 0; x < WIDTH; x++ {
			fmt.Printf("%c ", g.board[y][x])
		}
		fmt.Println("|")
	}
	fmt.Println("+" + string(make([]rune, WIDTH*2)) + "+")
	
	fmt.Printf("分数: %d\n", g.score)
	if g.gameOver {
		fmt.Println("游戏结束! 按 R 重新开始，按 Q 退出")
	} else {
		fmt.Println("使用 WASD 控制方向，按 Q 退出")
	}
}

func clearScreen() {
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/c", "cls")
	} else {
		cmd = exec.Command("clear")
	}
	cmd.Stdout = os.Stdout
	cmd.Run()
}

func (g *Game) restart() {
	g.snake = []Point{{WIDTH / 2, HEIGHT / 2}}
	g.direction = RIGHT
	g.gameOver = false
	g.score = 0
	g.generateFood()
}

func main() {
	rand.Seed(time.Now().UnixNano())
	
	game := NewGame()
	
	fmt.Println("=== 控制台贪吃蛇游戏 ===")
	fmt.Println("使用 WASD 控制方向")
	fmt.Println("按 Enter 开始游戏...")
	fmt.Scanln()
	
	// 游戏主循环
	go func() {
		for {
			if !game.gameOver {
				game.move()
			}
			game.render()
			time.Sleep(500 * time.Millisecond)
		}
	}()
	
	// 输入处理
	for {
		var input string
		fmt.Scanln(&input)
		
		if len(input) > 0 {
			switch input[0] {
			case 'w', 'W':
				if game.direction != DOWN {
					game.direction = UP
				}
			case 's', 'S':
				if game.direction != UP {
					game.direction = DOWN
				}
			case 'a', 'A':
				if game.direction != RIGHT {
					game.direction = LEFT
				}
			case 'd', 'D':
				if game.direction != LEFT {
					game.direction = RIGHT
				}
			case 'r', 'R':
				game.restart()
			case 'q', 'Q':
				fmt.Println("谢谢游戏!")
				return
			}
		}
	}
}
