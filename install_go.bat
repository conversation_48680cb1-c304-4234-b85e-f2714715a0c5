@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo Go语言自动安装脚本
echo ========================================
echo.

:: 检查是否已安装Go
go version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Go语言已安装
    for /f "tokens=3" %%i in ('go version 2^>nul') do echo 版本: %%i
    echo.
    goto :compile_game
)

echo 📥 正在下载Go语言安装包...
echo.

:: 设置Go版本和下载URL
set GO_VERSION=1.21.5
set GO_URL=https://golang.org/dl/go%GO_VERSION%.windows-amd64.msi
set GO_FILE=go%GO_VERSION%.windows-amd64.msi

:: 使用PowerShell下载Go安装包
echo 下载地址: %GO_URL%
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%GO_URL%' -OutFile '%GO_FILE%' -UseBasicParsing}" 2>nul

if not exist "%GO_FILE%" (
    echo ❌ 下载失败！
    echo.
    echo 请手动下载并安装Go语言：
    echo 1. 访问 https://golang.org/dl/
    echo 2. 下载 Windows 64位安装包
    echo 3. 运行安装程序
    echo 4. 重新运行此脚本
    pause
    exit /b 1
)

echo ✅ 下载完成！
echo.

echo 🚀 正在安装Go语言...
echo 请在安装向导中点击"下一步"完成安装
echo.

:: 运行安装程序
start /wait msiexec /i "%GO_FILE%" /quiet /norestart

:: 清理安装文件
del "%GO_FILE%" 2>nul

echo.
echo 📝 正在刷新环境变量...
:: 刷新环境变量
call refreshenv.cmd 2>nul

:: 重新检查Go安装
timeout /t 3 /nobreak >nul
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  需要重新启动命令提示符以加载环境变量
    echo.
    echo 请：
    echo 1. 关闭此窗口
    echo 2. 重新打开命令提示符
    echo 3. 运行: go version 验证安装
    echo 4. 然后运行此脚本编译游戏
    pause
    exit /b 0
)

echo ✅ Go语言安装成功！
for /f "tokens=3" %%i in ('go version') do echo 版本: %%i
echo.

:compile_game
echo ========================================
echo 编译贪吃蛇游戏
echo ========================================
echo.

:: 检查是否存在控制台版本
if exist snake_console.go (
    echo 📦 编译控制台版本游戏...
    
    :: 复制简单的go.mod
    copy go_simple.mod go.mod >nul 2>&1
    
    :: 编译控制台版本
    go build -o snake-console.exe snake_console.go
    if %errorlevel% equ 0 (
        echo ✅ 控制台版本编译成功！
        echo 生成文件: snake-console.exe
        echo.
        
        set /p RUN_CONSOLE="是否运行控制台版本游戏？(Y/N): "
        if /i "!RUN_CONSOLE!"=="Y" (
            echo.
            echo 🎮 启动控制台贪吃蛇游戏...
            echo 操作说明：
            echo • 使用 WASD 控制方向
            echo • 按 R 重新开始
            echo • 按 Q 退出游戏
            echo.
            pause
            start snake-console.exe
        )
    ) else (
        echo ❌ 控制台版本编译失败
    )
    echo.
)

:: 检查是否存在GUI版本
if exist main.go (
    echo 📦 编译GUI版本游戏...
    
    :: 恢复原始go.mod
    if exist go.mod.bak (
        copy go.mod.bak go.mod >nul 2>&1
    )
    
    :: 安装依赖
    echo 正在安装GUI依赖...
    go mod tidy
    
    if %errorlevel% equ 0 (
        :: 编译GUI版本
        go build -ldflags="-s -w" -o snake-game.exe main.go
        if %errorlevel% equ 0 (
            echo ✅ GUI版本编译成功！
            echo 生成文件: snake-game.exe
            echo.
            
            set /p RUN_GUI="是否运行GUI版本游戏？(Y/N): "
            if /i "!RUN_GUI!"=="Y" (
                echo.
                echo 🎮 启动GUI贪吃蛇游戏...
                start snake-game.exe
            )
        ) else (
            echo ❌ GUI版本编译失败（可能是网络问题）
            echo 💡 可以使用控制台版本: snake-console.exe
        )
    ) else (
        echo ❌ GUI依赖安装失败（可能是网络问题）
        echo 💡 可以使用控制台版本: snake-console.exe
    )
)

echo.
echo ========================================
echo 安装和编译完成！
echo ========================================
echo.

if exist snake-console.exe (
    echo ✅ 控制台版本: snake-console.exe
)
if exist snake-game.exe (
    echo ✅ GUI版本: snake-game.exe
)

echo.
echo 按任意键退出...
pause >nul
