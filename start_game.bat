@echo off
chcp 65001 >nul
echo ========================================
echo 贪吃蛇游戏启动器
echo ========================================
echo.

:: 检查Go环境
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Go语言环境
    echo.
    echo 正在启动自动安装程序...
    call install_go.bat
    exit /b
)

echo ✅ Go环境检测成功
for /f "tokens=3" %%i in ('go version') do echo Go版本: %%i
echo.

:: 检查是否有编译好的游戏
if exist snake-console.exe (
    echo ✅ 找到控制台版本游戏
    goto :run_console
)

if exist snake-game.exe (
    echo ✅ 找到GUI版本游戏
    goto :run_gui
)

:: 没有编译好的游戏，开始编译
echo 📦 未找到编译好的游戏，开始编译...
echo.

:: 编译控制台版本
if exist snake_console.go (
    echo 编译控制台版本...
    copy go_simple.mod go.mod >nul 2>&1
    go build -o snake-console.exe snake_console.go
    if %errorlevel% equ 0 (
        echo ✅ 控制台版本编译成功！
        goto :run_console
    ) else (
        echo ❌ 控制台版本编译失败
    )
)

:: 编译GUI版本
if exist main.go (
    echo 编译GUI版本...
    if exist go.mod.original (
        copy go.mod.original go.mod >nul 2>&1
    )
    go mod tidy >nul 2>&1
    go build -o snake-game.exe main.go >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ GUI版本编译成功！
        goto :run_gui
    ) else (
        echo ❌ GUI版本编译失败
    )
)

echo ❌ 编译失败，请检查源代码文件
pause
exit /b 1

:run_console
echo.
echo 🎮 启动控制台贪吃蛇游戏
echo.
echo 游戏操作：
echo • 使用 WASD 键控制蛇的移动方向
echo • W - 向上    S - 向下
echo • A - 向左    D - 向右
echo • R - 重新开始游戏
echo • Q - 退出游戏
echo.
echo 游戏规则：
echo • 控制蛇(O)吃食物(*)
echo • 每吃一个食物得10分
echo • 撞墙或撞到自己身体游戏结束
echo.
pause
cls
snake-console.exe
goto :end

:run_gui
echo.
echo 🎮 启动GUI贪吃蛇游戏
echo.
echo 游戏操作：
echo • 使用方向键控制蛇的移动
echo • 空格键重新开始游戏
echo • 点击"开始游戏"按钮重启
echo.
start snake-game.exe
goto :end

:end
echo.
echo 游戏结束，感谢游戏！
echo 按任意键退出...
pause >nul
