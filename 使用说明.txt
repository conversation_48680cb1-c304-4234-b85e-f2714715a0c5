剑网三缘起茶馆日常自动化脚本 - 快速使用指南
==============================================

📁 文件说明
-----------
主脚本.Q              - 主脚本文件（核心功能）
启动器.Q              - 图形界面启动器
快速设置向导.Q        - 新手配置向导
config.txt            - 配置文件
README.md             - 详细说明文档
使用说明.txt          - 本文件（快速指南）

🚀 快速开始（3步搞定）
--------------------
第一步：运行配置向导
- 双击打开按键精灵2014
- 导入并运行 "快速设置向导.Q"
- 按照提示配置坐标和延时

第二步：启动游戏
- 启动剑网三缘起游戏
- 登录角色并前往茶馆区域
- 确保能看到赵云睿和茶馆小童

第三步：运行脚本
- 在按键精灵中导入并运行 "主脚本.Q"
- 或者使用 "启动器.Q" 进行图形化操作

⚙️ 重要配置
-----------
坐标配置（必须）：
- 赵云睿坐标：茶馆任务NPC
- 茶馆小童坐标：子任务NPC
- 背包坐标：用于使用物品

延时配置（建议）：
- 网络好：基础延时 1000ms
- 网络一般：基础延时 1500ms
- 网络慢：基础延时 2000ms

🎯 支持的任务
------------
新人茶馆任务（10个）：
1. 送茶予客        6. 收集柴火
2. 动物肉块        7. 花茶材料
3. 打水煮茶        8. 客人包裹
4. 教训混混        9. 教训毛贼
5. 采集蜂蜜        10. 翻晒茶叶

进阶茶馆任务（5个）：
11. 青龙棋局       14. 江湖秘闻
12. 风雅对诗       15. 安顿流民
13. 调皮灵猫

材料任务（8个）：
16. 五香酱肉（上等里脊肉）
17. 芋粉扣肉（上等五花肉）
18. 天青石任务
19. 烟雨石任务
20. 香叶紫苏
21. 灵根白术
22. 古香缎任务
23. 织锦缎任务

⚠️ 注意事项
-----------
使用前确保：
✓ 角色位于茶馆区域（太原或阴山大草原）
✓ 游戏窗口处于前台且清晰可见
✓ 没有其他窗口遮挡游戏界面
✓ 网络连接稳定

脚本运行时：
✓ 不要操作鼠标键盘
✓ 不要最小化游戏窗口
✓ 不要切换到其他程序
✓ 保持电脑正常运行

🔧 常见问题
-----------
Q: 脚本点击位置不准确？
A: 重新运行"快速设置向导.q"配置坐标

Q: 脚本运行太快或太慢？
A: 在config.txt中调整延时设置

Q: 找不到游戏窗口？
A: 确保游戏标题为"剑网3"或"JX3"

Q: 任务执行失败？
A: 查看jx3_log.txt日志文件了解详情

Q: 材料任务跳过？
A: 确认背包中有相应的材料物品

📝 日志文件
-----------
脚本会自动生成 jx3_log.txt 日志文件
记录详细的执行过程和错误信息
遇到问题时请优先查看日志

🛠️ 高级功能
-----------
使用启动器.Q可以：
- 图形化管理脚本
- 实时获取坐标
- 测试游戏连接
- 查看日志文件
- 修改配置设置

📞 技术支持
-----------
如果遇到问题：
1. 查看 jx3_log.txt 日志文件
2. 检查 config.txt 配置是否正确
3. 确认游戏版本和脚本兼容性
4. 重新运行快速设置向导

💡 使用技巧
-----------
- 建议在人少的时间段使用
- 材料任务需要提前准备材料
- 可以通过交易行购买所需材料
- 定期检查和更新坐标配置
- 根据网络状况调整延时设置

🎉 祝您使用愉快！
================
