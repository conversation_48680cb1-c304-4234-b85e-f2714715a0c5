//==============================================================================
// 剑网三茶馆日常脚本 - 快速设置向导
// 版本: 1.0
// 功能: 帮助用户快速配置脚本所需的坐标和设置
//==============================================================================

// 全局变量
Dim 当前步骤, 配置数据(20)

// 初始化向导
Sub 初始化向导()
    当前步骤 = 1
    
    MessageBox "欢迎使用剑网三茶馆日常脚本设置向导！" & vbCrLf & vbCrLf & _
              "本向导将帮助您完成以下配置：" & vbCrLf & _
              "1. 检测游戏运行状态" & vbCrLf & _
              "2. 配置主要NPC坐标" & vbCrLf & _
              "3. 设置延时参数" & vbCrLf & _
              "4. 保存配置文件" & vbCrLf & vbCrLf & _
              "请确保剑网三游戏已启动并位于茶馆区域"
    
    Call 开始向导流程()
End Sub

// 开始向导流程
Sub 开始向导流程()
    // 步骤1: 检测游戏
    Call 步骤1_检测游戏()
    
    // 步骤2: 配置赵云睿坐标
    Call 步骤2_配置赵云睿坐标()
    
    // 步骤3: 配置茶馆小童坐标
    Call 步骤3_配置茶馆小童坐标()
    
    // 步骤4: 配置背包坐标
    Call 步骤4_配置背包坐标()
    
    // 步骤5: 设置延时
    Call 步骤5_设置延时()
    
    // 步骤6: 保存配置
    Call 步骤6_保存配置()
    
    // 完成
    Call 完成向导()
End Sub

// 步骤1: 检测游戏
Sub 步骤1_检测游戏()
    MessageBox "步骤 1/6: 检测游戏状态"
    
    Dim 游戏窗口句柄
    游戏窗口句柄 = FindWindow("", "剑网3")
    If 游戏窗口句柄 = 0 Then
        游戏窗口句柄 = FindWindow("", "JX3")
    End If
    
    If 游戏窗口句柄 > 0 Then
        MessageBox "✅ 游戏检测成功！" & vbCrLf & _
                  "游戏窗口句柄: " & 游戏窗口句柄 & vbCrLf & vbCrLf & _
                  "请确保角色位于茶馆区域，可以看到赵云睿和茶馆小童"
        配置数据(1) = 游戏窗口句柄
    Else
        MessageBox "❌ 未检测到游戏窗口！" & vbCrLf & vbCrLf & _
                  "请启动剑网三游戏后重新运行向导"
        Exit Sub
    End If
End Sub

// 步骤2: 配置赵云睿坐标
Sub 步骤2_配置赵云睿坐标()
    MessageBox "步骤 2/6: 配置赵云睿坐标" & vbCrLf & vbCrLf & _
              "赵云睿是茶馆任务的主要NPC" & vbCrLf & vbCrLf & _
              "操作步骤：" & vbCrLf & _
              "1. 将鼠标移动到赵云睿身上" & vbCrLf & _
              "2. 按下 F1 键获取坐标" & vbCrLf & _
              "3. 确认坐标无误"
    
    // 等待F1键
    Do
        If GetAsyncKeyState(112) Then  // F1键
            Dim 鼠标X, 鼠标Y
            GetCursorPos 鼠标X, 鼠标Y
            配置数据(2) = 鼠标X
            配置数据(3) = 鼠标Y
            
            MessageBox "赵云睿坐标已记录: (" & 鼠标X & "," & 鼠标Y & ")" & vbCrLf & _
                      "请确认坐标位置正确"
            Exit Do
        End If
        Delay 100
    Loop
End Sub

// 步骤3: 配置茶馆小童坐标
Sub 步骤3_配置茶馆小童坐标()
    MessageBox "步骤 3/6: 配置茶馆小童坐标" & vbCrLf & vbCrLf & _
              "茶馆小童负责分发各种子任务" & vbCrLf & vbCrLf & _
              "操作步骤：" & vbCrLf & _
              "1. 将鼠标移动到茶馆小童身上" & vbCrLf & _
              "2. 按下 F1 键获取坐标" & vbCrLf & _
              "3. 确认坐标无误"
    
    // 等待F1键
    Do
        If GetAsyncKeyState(112) Then  // F1键
            Dim 鼠标X, 鼠标Y
            GetCursorPos 鼠标X, 鼠标Y
            配置数据(4) = 鼠标X
            配置数据(5) = 鼠标Y
            
            MessageBox "茶馆小童坐标已记录: (" & 鼠标X & "," & 鼠标Y & ")" & vbCrLf & _
                      "请确认坐标位置正确"
            Exit Do
        End If
        Delay 100
    Loop
End Sub

// 步骤4: 配置背包坐标
Sub 步骤4_配置背包坐标()
    MessageBox "步骤 4/6: 配置背包坐标" & vbCrLf & vbCrLf & _
              "背包坐标用于使用任务物品" & vbCrLf & vbCrLf & _
              "操作步骤：" & vbCrLf & _
              "1. 按 B 键打开背包" & vbCrLf & _
              "2. 将鼠标移动到背包左上角" & vbCrLf & _
              "3. 按下 F1 键获取坐标"
    
    // 等待F1键
    Do
        If GetAsyncKeyState(112) Then  // F1键
            Dim 鼠标X, 鼠标Y
            GetCursorPos 鼠标X, 鼠标Y
            配置数据(6) = 鼠标X
            配置数据(7) = 鼠标Y
            
            MessageBox "背包坐标已记录: (" & 鼠标X & "," & 鼠标Y & ")" & vbCrLf & _
                      "请确认坐标位置正确"
            Exit Do
        End If
        Delay 100
    Loop
End Sub

// 步骤5: 设置延时
Sub 步骤5_设置延时()
    MessageBox "步骤 5/6: 设置延时参数" & vbCrLf & vbCrLf & _
              "延时设置影响脚本执行的稳定性"
    
    Dim 网络状况
    网络状况 = InputBox("请选择您的网络状况：" & vbCrLf & vbCrLf & _
                       "1 - 网络良好 (基础延时1000ms)" & vbCrLf & _
                       "2 - 网络一般 (基础延时1500ms)" & vbCrLf & _
                       "3 - 网络较慢 (基础延时2000ms)" & vbCrLf & vbCrLf & _
                       "请输入选项 (1-3):", "延时设置", "1")
    
    Select Case 网络状况
        Case "1"
            配置数据(8) = 1000
        Case "2"
            配置数据(8) = 1500
        Case "3"
            配置数据(8) = 2000
        Case Else
            配置数据(8) = 1000
    End Select
    
    配置数据(9) = 配置数据(8) * 3  // 长延时
    配置数据(10) = 配置数据(8) / 2  // 短延时
    
    MessageBox "延时设置完成：" & vbCrLf & _
              "基础延时: " & 配置数据(8) & "ms" & vbCrLf & _
              "长延时: " & 配置数据(9) & "ms" & vbCrLf & _
              "短延时: " & 配置数据(10) & "ms"
End Sub

// 步骤6: 保存配置
Sub 步骤6_保存配置()
    MessageBox "步骤 6/6: 保存配置文件"
    
    // 保存坐标配置
    WriteIni "config.txt", "坐标", "赵云睿X", 配置数据(2)
    WriteIni "config.txt", "坐标", "赵云睿Y", 配置数据(3)
    WriteIni "config.txt", "坐标", "茶馆小童X", 配置数据(4)
    WriteIni "config.txt", "坐标", "茶馆小童Y", 配置数据(5)
    WriteIni "config.txt", "坐标", "背包X", 配置数据(6)
    WriteIni "config.txt", "坐标", "背包Y", 配置数据(7)
    
    // 保存延时配置
    WriteIni "config.txt", "延时", "基础延时", 配置数据(8)
    WriteIni "config.txt", "延时", "长延时", 配置数据(9)
    WriteIni "config.txt", "延时", "短延时", 配置数据(10)
    
    // 保存其他默认配置
    WriteIni "config.txt", "设置", "自动重试次数", "3"
    WriteIni "config.txt", "设置", "最大等待时间", "10000"
    WriteIni "config.txt", "设置", "是否启用安全模式", "1"
    
    MessageBox "配置文件保存成功！"
End Sub

// 完成向导
Sub 完成向导()
    MessageBox "🎉 设置向导完成！" & vbCrLf & vbCrLf & _
              "配置摘要：" & vbCrLf & _
              "• 赵云睿坐标: (" & 配置数据(2) & "," & 配置数据(3) & ")" & vbCrLf & _
              "• 茶馆小童坐标: (" & 配置数据(4) & "," & 配置数据(5) & ")" & vbCrLf & _
              "• 背包坐标: (" & 配置数据(6) & "," & 配置数据(7) & ")" & vbCrLf & _
              "• 基础延时: " & 配置数据(8) & "ms" & vbCrLf & vbCrLf & _
              "现在您可以：" & vbCrLf & _
              "• 运行 启动器.q 来管理脚本" & vbCrLf & _
              "• 直接运行 jx3_chaguan_daily.q 主脚本" & vbCrLf & _
              "• 查看 README.md 了解详细使用说明"
    
    Dim 立即运行
    立即运行 = MessageBox("是否立即运行茶馆日常脚本？", vbYesNo)
    
    If 立即运行 = vbYes Then
        RunScript "jx3_chaguan_daily.q"
    End If
End Sub

// 主程序入口
Sub Main()
    Call 初始化向导()
End Sub

// 启动向导
Call Main()
