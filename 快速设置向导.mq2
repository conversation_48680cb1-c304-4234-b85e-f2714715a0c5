//==============================================================================
// 剑网三茶馆日常脚本 - 快速设置向导
// 版本: 1.0
// 功能: 帮助用户快速配置脚本所需的坐标和设置
//==============================================================================

// 全局变量
Dim 当前步骤, 总步骤数, 配置数据(50)
Dim 向导窗口句柄

// 初始化向导
Sub 初始化向导()
    当前步骤 = 1
    总步骤数 = 8
    
    Call 创建向导界面()
    Call 显示当前步骤()
End Sub

// 创建向导界面
Sub 创建向导界面()
    向导窗口句柄 = CreateWindow("茶馆日常脚本设置向导", 500, 400)
    
    // 添加标题
    Call AddLabel(向导窗口句柄, "剑网三茶馆日常脚本设置向导", 150, 20, 200, 25, 1001)
    
    // 添加步骤显示
    Call AddLabel(向导窗口句柄, "步骤 1/8", 20, 60, 100, 20, 1002)
    
    // 添加说明文本
    Call AddLabel(向导窗口句柄, "说明文本区域", 20, 90, 460, 200, 1003)
    
    // 添加按钮
    Call AddButton(向导窗口句柄, "上一步", 100, 320, 80, 30, 2001)
    Call AddButton(向导窗口句柄, "下一步", 200, 320, 80, 30, 2002)
    Call AddButton(向导窗口句柄, "完成", 300, 320, 80, 30, 2003)
    Call AddButton(向导窗口句柄, "取消", 400, 320, 80, 30, 2004)
    
    ShowWindow 向导窗口句柄
End Sub

// 显示当前步骤
Sub 显示当前步骤()
    Call UpdateLabel(向导窗口句柄, 1002, "步骤 " & 当前步骤 & "/" & 总步骤数)
    
    Select Case 当前步骤
        Case 1
            Call 步骤1_欢迎界面()
        Case 2
            Call 步骤2_游戏检测()
        Case 3
            Call 步骤3_赵云睿坐标()
        Case 4
            Call 步骤4_茶馆小童坐标()
        Case 5
            Call 步骤5_背包坐标()
        Case 6
            Call 步骤6_延时设置()
        Case 7
            Call 步骤7_测试配置()
        Case 8
            Call 步骤8_完成设置()
    End Select
End Sub

// 步骤1: 欢迎界面
Sub 步骤1_欢迎界面()
    Dim 说明文本
    说明文本 = "欢迎使用剑网三茶馆日常脚本设置向导！" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "本向导将帮助您完成以下配置：" & vbCrLf
    说明文本 = 说明文本 & "1. 检测游戏运行状态" & vbCrLf
    说明文本 = 说明文本 & "2. 配置主要NPC坐标" & vbCrLf
    说明文本 = 说明文本 & "3. 设置界面元素坐标" & vbCrLf
    说明文本 = 说明文本 & "4. 调整延时参数" & vbCrLf
    说明文本 = 说明文本 & "5. 测试配置有效性" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "请确保：" & vbCrLf
    说明文本 = 说明文本 & "• 剑网三游戏已启动并登录角色" & vbCrLf
    说明文本 = 说明文本 & "• 角色位于茶馆区域（太原或阴山大草原）" & vbCrLf
    说明文本 = 说明文本 & "• 游戏窗口处于前台显示状态"
    
    Call UpdateLabel(向导窗口句柄, 1003, 说明文本)
End Sub

// 步骤2: 游戏检测
Sub 步骤2_游戏检测()
    Dim 说明文本, 游戏窗口
    
    游戏窗口 = FindWindow("", "剑网3")
    If 游戏窗口 = 0 Then
        游戏窗口 = FindWindow("", "JX3")
    End If
    
    If 游戏窗口 > 0 Then
        说明文本 = "✅ 游戏检测成功！" & vbCrLf & vbCrLf
        说明文本 = 说明文本 & "检测到游戏窗口句柄: " & 游戏窗口 & vbCrLf & vbCrLf
        说明文本 = 说明文本 & "请确保：" & vbCrLf
        说明文本 = 说明文本 & "• 角色已进入游戏世界" & vbCrLf
        说明文本 = 说明文本 & "• 当前位于茶馆区域" & vbCrLf
        说明文本 = 说明文本 & "• 可以看到赵云睿和茶馆小童" & vbCrLf & vbCrLf
        说明文本 = 说明文本 & "点击'下一步'继续配置坐标"
        
        配置数据(1) = 游戏窗口
    Else
        说明文本 = "❌ 未检测到游戏窗口！" & vbCrLf & vbCrLf
        说明文本 = 说明文本 & "请检查：" & vbCrLf
        说明文本 = 说明文本 & "• 剑网三游戏是否已启动" & vbCrLf
        说明文本 = 说明文本 & "• 游戏窗口标题是否为'剑网3'或'JX3'" & vbCrLf
        说明文本 = 说明文本 & "• 游戏是否处于前台显示" & vbCrLf & vbCrLf
        说明文本 = 说明文本 & "请启动游戏后重新检测"
    End If
    
    Call UpdateLabel(向导窗口句柄, 1003, 说明文本)
End Sub

// 步骤3: 赵云睿坐标
Sub 步骤3_赵云睿坐标()
    Dim 说明文本
    说明文本 = "配置赵云睿坐标" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "赵云睿是茶馆任务的主要NPC，负责发放和回收茶馆任务。" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "操作步骤：" & vbCrLf
    说明文本 = 说明文本 & "1. 将鼠标移动到赵云睿身上" & vbCrLf
    说明文本 = 说明文本 & "2. 按下 F1 键获取坐标" & vbCrLf
    说明文本 = 说明文本 & "3. 确认坐标后点击'下一步'" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "当前坐标: 等待获取..." & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "提示: 建议将鼠标放在赵云睿的中心位置"
    
    Call UpdateLabel(向导窗口句柄, 1003, 说明文本)
    
    // 监听F1键
    Call 监听坐标获取(3)
End Sub

// 步骤4: 茶馆小童坐标
Sub 步骤4_茶馆小童坐标()
    Dim 说明文本
    说明文本 = "配置茶馆小童坐标" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "茶馆小童负责分发各种子任务。" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "操作步骤：" & vbCrLf
    说明文本 = 说明文本 & "1. 将鼠标移动到茶馆小童身上" & vbCrLf
    说明文本 = 说明文本 & "2. 按下 F1 键获取坐标" & vbCrLf
    说明文本 = 说明文本 & "3. 确认坐标后点击'下一步'" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "当前坐标: 等待获取..." & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "提示: 茶馆小童通常在赵云睿附近"
    
    Call UpdateLabel(向导窗口句柄, 1003, 说明文本)
    
    // 监听F1键
    Call 监听坐标获取(4)
End Sub

// 步骤5: 背包坐标
Sub 步骤5_背包坐标()
    Dim 说明文本
    说明文本 = "配置背包坐标" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "背包坐标用于使用任务物品和检测材料。" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "操作步骤：" & vbCrLf
    说明文本 = 说明文本 & "1. 按 B 键打开背包" & vbCrLf
    说明文本 = 说明文本 & "2. 将鼠标移动到背包左上角" & vbCrLf
    说明文本 = 说明文本 & "3. 按下 F1 键获取坐标" & vbCrLf
    说明文本 = 说明文本 & "4. 确认坐标后点击'下一步'" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "当前坐标: 等待获取..." & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "提示: 请确保背包处于打开状态"
    
    Call UpdateLabel(向导窗口句柄, 1003, 说明文本)
    
    // 监听F1键
    Call 监听坐标获取(5)
End Sub

// 步骤6: 延时设置
Sub 步骤6_延时设置()
    Dim 说明文本
    说明文本 = "配置延时设置" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "延时设置影响脚本执行的稳定性和速度。" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "推荐设置：" & vbCrLf
    说明文本 = 说明文本 & "• 基础延时: 1000ms (网络良好)" & vbCrLf
    说明文本 = 说明文本 & "• 基础延时: 1500ms (网络一般)" & vbCrLf
    说明文本 = 说明文本 & "• 基础延时: 2000ms (网络较慢)" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "• 长延时: 基础延时 × 3" & vbCrLf
    说明文本 = 说明文本 & "• 短延时: 基础延时 ÷ 2" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "当前设置: 基础延时 1000ms"
    
    Call UpdateLabel(向导窗口句柄, 1003, 说明文本)
    
    // 设置默认延时
    配置数据(10) = 1000  // 基础延时
    配置数据(11) = 3000  // 长延时
    配置数据(12) = 500   // 短延时
End Sub

// 步骤7: 测试配置
Sub 步骤7_测试配置()
    Dim 说明文本
    说明文本 = "测试配置有效性" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "即将测试已配置的坐标和设置..." & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "测试项目：" & vbCrLf
    说明文本 = 说明文本 & "✅ 游戏窗口连接" & vbCrLf
    说明文本 = 说明文本 & "✅ 赵云睿坐标: (" & 配置数据(3) & "," & 配置数据(4) & ")" & vbCrLf
    说明文本 = 说明文本 & "✅ 茶馆小童坐标: (" & 配置数据(5) & "," & 配置数据(6) & ")" & vbCrLf
    说明文本 = 说明文本 & "✅ 背包坐标: (" & 配置数据(7) & "," & 配置数据(8) & ")" & vbCrLf
    说明文本 = 说明文本 & "✅ 延时设置: " & 配置数据(10) & "ms" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "点击'下一步'保存配置并完成设置"
    
    Call UpdateLabel(向导窗口句柄, 1003, 说明文本)
End Sub

// 步骤8: 完成设置
Sub 步骤8_完成设置()
    Dim 说明文本
    
    // 保存配置到文件
    Call 保存配置到文件()
    
    说明文本 = "🎉 配置完成！" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "配置文件已保存到 config.txt" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "现在您可以：" & vbCrLf
    说明文本 = 说明文本 & "• 运行主脚本 jx3_chaguan_daily.mq2" & vbCrLf
    说明文本 = 说明文本 & "• 使用启动器 启动器.mq2" & vbCrLf
    说明文本 = 说明文本 & "• 查看使用说明 README.md" & vbCrLf & vbCrLf
    说明文本 = 说明文本 & "温馨提示：" & vbCrLf
    说明文本 = 说明文本 & "• 首次运行建议先测试几个任务" & vbCrLf
    说明文本 = 说明文本 & "• 如有问题可重新运行此向导" & vbCrLf
    说明文本 = 说明文本 & "• 记得查看日志文件了解执行状态"
    
    Call UpdateLabel(向导窗口句柄, 1003, 说明文本)
End Sub

// 监听坐标获取
Sub 监听坐标获取(步骤编号)
    // 在后台监听F1键
    Do
        If GetAsyncKeyState(112) Then  // F1键
            Dim 鼠标X, 鼠标Y
            GetCursorPos 鼠标X, 鼠标Y
            
            // 保存坐标
            配置数据(步骤编号 * 2 - 1) = 鼠标X
            配置数据(步骤编号 * 2) = 鼠标Y
            
            // 更新显示
            Call 更新坐标显示(步骤编号, 鼠标X, 鼠标Y)
            Exit Do
        End If
        Delay 100
    Loop
End Sub

// 更新坐标显示
Sub 更新坐标显示(步骤编号, X, Y)
    Dim 当前文本, 新文本
    当前文本 = GetLabelText(向导窗口句柄, 1003)
    新文本 = Replace(当前文本, "当前坐标: 等待获取...", "当前坐标: (" & X & "," & Y & ") ✅")
    Call UpdateLabel(向导窗口句柄, 1003, 新文本)
End Sub

// 保存配置到文件
Sub 保存配置到文件()
    WriteIni "config.txt", "坐标", "赵云睿X", 配置数据(5)
    WriteIni "config.txt", "坐标", "赵云睿Y", 配置数据(6)
    WriteIni "config.txt", "坐标", "茶馆小童X", 配置数据(7)
    WriteIni "config.txt", "坐标", "茶馆小童Y", 配置数据(8)
    WriteIni "config.txt", "坐标", "背包X", 配置数据(9)
    WriteIni "config.txt", "坐标", "背包Y", 配置数据(10)
    WriteIni "config.txt", "延时", "基础延时", 配置数据(10)
    WriteIni "config.txt", "延时", "长延时", 配置数据(11)
    WriteIni "config.txt", "延时", "短延时", 配置数据(12)
End Sub

// 按钮事件处理
Sub 处理按钮事件(按钮ID)
    Select Case 按钮ID
        Case 2001  // 上一步
            If 当前步骤 > 1 Then
                当前步骤 = 当前步骤 - 1
                Call 显示当前步骤()
            End If
        Case 2002  // 下一步
            If 当前步骤 < 总步骤数 Then
                当前步骤 = 当前步骤 + 1
                Call 显示当前步骤()
            End If
        Case 2003  // 完成
            MessageBox "设置向导已完成！"
            CloseWindow 向导窗口句柄
        Case 2004  // 取消
            CloseWindow 向导窗口句柄
    End Select
End Sub

// 主程序
Sub Main()
    Call 初始化向导()
    
    // 消息循环
    Do
        Delay 100
    Loop While WindowExists(向导窗口句柄)
End Sub

// 启动向导
Call Main()
