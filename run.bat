@echo off
chcp 65001 >nul
echo ========================================
echo 贪吃蛇游戏启动器
echo ========================================
echo.

:: 检查是否存在编译好的游戏
if exist snake-game.exe (
    echo ✅ 找到游戏文件: snake-game.exe
    echo 🚀 正在启动贪吃蛇游戏...
    echo.
    echo 游戏操作提示：
    echo • 方向键 ↑↓←→ - 控制蛇的移动
    echo • 空格键 - 重新开始游戏
    echo • 点击"开始游戏"按钮 - 重启游戏
    echo.
    echo 游戏规则：
    echo • 控制绿色的蛇吃红色食物
    echo • 每吃一个食物得10分，蛇身增长
    echo • 撞墙或撞到自己身体游戏结束
    echo.
    start snake-game.exe
    echo 游戏已启动！
) else (
    echo ❌ 未找到游戏文件 snake-game.exe
    echo.
    echo 请先编译游戏：
    echo 1. 运行 build.bat 编译游戏
    echo 2. 或者手动编译: go build -o snake-game.exe main.go
    echo.
    
    :: 检查Go环境
    go version >nul 2>&1
    if %errorlevel% equ 0 (
        echo 检测到Go环境，是否现在编译游戏？
        set /p BUILD_NOW="输入 Y 开始编译，其他键退出: "
        if /i "!BUILD_NOW!"=="Y" (
            echo.
            echo 正在编译游戏...
            call build.bat
        )
    ) else (
        echo 未检测到Go环境，请先安装Go语言。
        echo 查看 INSTALL_GUIDE.md 获取安装指南。
    )
)

echo.
echo 按任意键退出...
pause >nul
