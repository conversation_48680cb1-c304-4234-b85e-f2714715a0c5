# 🐍 贪吃蛇游戏 - 准备就绪！

## 🎉 游戏已准备完毕

我已经为您创建了完整的贪吃蛇游戏项目，包含多个版本和自动化脚本。

## 📁 项目文件说明

### 🎮 游戏版本
1. **simple_snake.go** - 简化版贪吃蛇（推荐）
   - 纯控制台界面
   - 无外部依赖
   - 易于编译和运行

2. **snake_console.go** - 控制台版贪吃蛇
   - 带清屏功能
   - 更好的视觉效果

3. **main.go** - GUI版贪吃蛇
   - 图形界面版本
   - 需要网络下载依赖

### 🚀 启动脚本
- **play_snake.bat** - 一键启动游戏（推荐）
- **start_game.bat** - 游戏启动器
- **install_go.bat** - Go语言自动安装
- **build.bat** - 编译脚本
- **run.bat** - 运行脚本

### 📄 配置文件
- **go.mod** - Go模块配置
- **Makefile** - 跨平台构建脚本

## 🚀 快速开始

### 方法1: 一键启动（最简单）
```bash
# 双击运行或在命令行执行
play_snake.bat
```

### 方法2: 手动步骤
1. **安装Go语言**（如果未安装）
   - 访问 https://golang.org/dl/
   - 下载并安装Windows版本

2. **编译游戏**
   ```bash
   go build -o simple_snake.exe simple_snake.go
   ```

3. **运行游戏**
   ```bash
   simple_snake.exe
   ```

## 🎮 游戏操作

### 简化版游戏 (simple_snake.exe)
- **w** - 向上移动
- **a** - 向左移动
- **s** - 向下移动
- **d** - 向右移动
- **r** - 重新开始
- **q** - 退出游戏
- **回车** - 确认输入

### 控制台版游戏 (snake-console.exe)
- **WASD** - 控制方向
- **R** - 重新开始
- **Q** - 退出

### GUI版游戏 (snake-game.exe)
- **方向键** - 控制移动
- **空格键** - 重新开始
- **鼠标点击** - 界面操作

## 🎯 游戏规则

1. **目标**: 控制蛇吃食物，获得高分
2. **移动**: 蛇会持续向当前方向移动
3. **成长**: 吃到食物(*)后蛇身增长，得10分
4. **失败条件**:
   - 撞到边界墙壁
   - 撞到自己的身体
5. **符号说明**:
   - `O` - 蛇头
   - `o` - 蛇身
   - `*` - 食物

## 🔧 故障排除

### 问题1: "go不是内部或外部命令"
**解决方案**: 
- 安装Go语言环境
- 运行 `install_go.bat` 自动安装
- 或手动从 https://golang.org/dl/ 下载安装

### 问题2: 编译失败
**解决方案**:
- 确保Go版本 >= 1.21
- 检查源代码文件是否完整
- 尝试运行 `go mod tidy`

### 问题3: 游戏运行异常
**解决方案**:
- 确保命令提示符支持UTF-8编码
- 尝试不同版本的游戏
- 检查防火墙和杀毒软件设置

### 问题4: 网络依赖下载失败
**解决方案**:
- 使用简化版游戏（无网络依赖）
- 配置Go代理：`go env -w GOPROXY=https://goproxy.cn,direct`

## 📊 游戏特性对比

| 特性 | 简化版 | 控制台版 | GUI版 |
|------|--------|----------|-------|
| 编译难度 | ⭐ | ⭐⭐ | ⭐⭐⭐ |
| 运行要求 | 低 | 低 | 中等 |
| 视觉效果 | 基础 | 良好 | 优秀 |
| 网络依赖 | 无 | 无 | 有 |
| 推荐程度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🎊 开始游戏

现在一切都准备就绪！选择您喜欢的方式开始游戏：

1. **最简单**: 双击 `play_snake.bat`
2. **手动编译**: 运行 `build.bat`
3. **命令行**: `go run simple_snake.go`

## 🌟 享受游戏

祝您游戏愉快！这个贪吃蛇游戏不仅好玩，还是学习Go语言的好例子。

如果您想要修改游戏（比如调整速度、大小等），可以编辑源代码文件中的常量值。

**Happy Coding & Gaming!** 🐍🎮
