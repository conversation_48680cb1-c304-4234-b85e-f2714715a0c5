# Go语言环境安装指南

由于您的系统中没有安装Go语言环境，需要先安装Go才能编译和运行贪吃蛇游戏。

## 🚀 快速安装Go语言

### Windows系统安装

#### 方法1: 官方安装包（推荐）

1. **下载Go安装包**
   - 访问官网：https://golang.org/dl/
   - 下载最新版本的Windows安装包（如：go1.21.x.windows-amd64.msi）

2. **安装Go**
   - 双击下载的.msi文件
   - 按照安装向导完成安装
   - 默认安装路径：`C:\Program Files\Go`

3. **验证安装**
   - 打开新的命令提示符或PowerShell
   - 运行：`go version`
   - 应该显示Go版本信息

#### 方法2: 使用Chocolatey

如果您已安装Chocolatey包管理器：

```powershell
# 以管理员身份运行PowerShell
choco install golang
```

#### 方法3: 使用Scoop

如果您已安装Scoop包管理器：

```powershell
scoop install go
```

### 环境变量配置

安装完成后，通常会自动配置环境变量。如果没有，请手动添加：

1. **GOROOT**: Go安装目录（如：`C:\Program Files\Go`）
2. **GOPATH**: Go工作空间（如：`C:\Users\<USER>\go`）
3. **PATH**: 添加 `%GOROOT%\bin` 和 `%GOPATH%\bin`

## 🎮 安装完成后编译游戏

安装Go语言环境后，按以下步骤编译游戏：

### 1. 安装依赖
```bash
go mod tidy
```

### 2. 编译游戏
```bash
go build -o snake-game.exe main.go
```

### 3. 运行游戏
```bash
./snake-game.exe
```

或者使用Makefile：
```bash
make build
make run
```

## 🔧 故障排除

### 常见问题

1. **"go不是内部或外部命令"**
   - 重新启动命令提示符/PowerShell
   - 检查环境变量PATH是否包含Go的bin目录
   - 重新安装Go

2. **网络问题导致依赖下载失败**
   ```bash
   # 设置Go代理（中国用户）
   go env -w GOPROXY=https://goproxy.cn,direct
   go env -w GOSUMDB=sum.golang.google.cn
   ```

3. **权限问题**
   - 以管理员身份运行命令提示符
   - 确保有写入权限

## 📦 预编译版本

如果您不想安装Go环境，我可以为您提供预编译的可执行文件。

### 系统要求
- Windows 10/11 (64位)
- 支持图形界面
- 至少4MB可用内存

### 运行说明
1. 下载预编译的 `snake-game.exe`
2. 双击运行即可开始游戏
3. 使用方向键控制，空格键重新开始

## 🎯 游戏特性

- 经典贪吃蛇玩法
- 现代化图形界面
- 实时分数显示
- 键盘控制
- 碰撞检测
- 游戏重启功能

## 📞 技术支持

如果在安装或运行过程中遇到问题：

1. 确保系统满足最低要求
2. 检查防火墙和杀毒软件设置
3. 尝试以管理员身份运行
4. 查看错误日志信息

---

**安装完成后，您就可以享受贪吃蛇游戏了！** 🐍🎮
