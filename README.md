# 贪吃蛇游戏 (Snake Game)

一个使用 Go 语言和 Fyne GUI 框架开发的贪吃蛇小游戏。

## 功能特点

- 🎮 经典贪吃蛇游戏玩法
- 🖥️ 现代化的图形用户界面
- ⌨️ 键盘控制支持
- 📊 实时分数显示
- 🔄 游戏重启功能
- 🎯 碰撞检测
- 🍎 随机食物生成

## 游戏截图

游戏界面包含：
- 20x20 的游戏网格
- 绿色的蛇（蛇头为亮绿色，蛇身为深绿色）
- 红色的食物
- 分数显示
- 游戏状态提示
- 控制按钮

## 安装和运行

### 🚀 快速开始（Windows用户）

**方法1: 使用批处理脚本（推荐）**
```bash
# 编译游戏
build.bat

# 运行游戏
run.bat
```

**方法2: 使用Makefile**
```bash
# 编译并运行
make build-run

# 或者分步执行
make build
make run
```

### 📋 前置要求

1. **安装 Go 语言**
   - 下载并安装 Go 1.21 或更高版本
   - 官网：https://golang.org/dl/
   - 详细安装指南：查看 `INSTALL_GUIDE.md`

2. **安装依赖**
   ```bash
   go mod tidy
   ```

### 🎮 运行游戏

**直接运行（开发模式）**
```bash
go run main.go
```

**编译后运行（推荐）**
```bash
# 编译
go build -o snake-game.exe main.go

# 运行
snake-game.exe      # Windows
./snake-game        # Linux/macOS
```

## 游戏操作

### 键盘控制
- **方向键 ↑↓←→** - 控制蛇的移动方向
- **空格键** - 重新开始游戏

### 鼠标操作
- **开始游戏按钮** - 重新开始游戏
- **暂停/继续按钮** - 暂停或继续游戏（功能待完善）

## 游戏规则

1. **目标**：控制蛇吃到红色食物，获得分数
2. **移动**：蛇会持续向当前方向移动
3. **成长**：每吃到一个食物，蛇身增长一节，分数增加10分
4. **游戏结束条件**：
   - 蛇撞到边界墙壁
   - 蛇撞到自己的身体
5. **防反向**：蛇不能立即反向移动（如向上移动时不能直接向下）

## 技术实现

### 主要技术栈
- **Go 语言** - 主要编程语言
- **Fyne v2** - GUI 框架
- **Canvas** - 图形绘制
- **Container** - 布局管理

### 核心组件

1. **Game 结构体** - 游戏状态管理
   ```go
   type Game struct {
       snake     []Point      // 蛇的身体坐标
       food      Point        // 食物坐标
       direction Direction    // 移动方向
       gameOver  bool         // 游戏结束状态
       score     int          // 当前分数
       // ... UI 组件
   }
   ```

2. **Point 结构体** - 坐标表示
   ```go
   type Point struct {
       X, Y int
   }
   ```

3. **Direction 枚举** - 方向定义
   ```go
   type Direction int
   const (
       UP, DOWN, LEFT, RIGHT Direction = iota, iota, iota, iota
   )
   ```

### 主要功能模块

- **游戏逻辑** (`move()`) - 处理蛇的移动、碰撞检测、食物消费
- **UI 渲染** (`updateDisplay()`) - 更新游戏画面显示
- **输入处理** - 键盘事件监听和方向控制
- **游戏循环** - 定时器驱动的游戏状态更新

## 项目结构

```
snake-game/
├── main.go          # 主程序文件
├── go.mod           # Go 模块文件
├── go.sum           # 依赖版本锁定文件
└── README.md        # 项目说明文档
```

## 自定义配置

可以通过修改常量来调整游戏参数：

```go
const (
    GRID_WIDTH  = 20    // 网格宽度
    GRID_HEIGHT = 20    // 网格高度
    CELL_SIZE   = 20    // 每个格子的像素大小
)
```

游戏速度可以通过修改定时器间隔调整：
```go
ticker := time.NewTicker(200 * time.Millisecond)  // 200ms = 5FPS
```

## 可能的改进

- [ ] 添加暂停/继续功能
- [ ] 添加难度等级选择
- [ ] 添加最高分记录
- [ ] 添加音效
- [ ] 添加更多视觉效果
- [ ] 支持触摸屏操作
- [ ] 添加游戏设置界面

## 故障排除

### 常见问题

1. **编译错误**
   - 确保 Go 版本 >= 1.21
   - 运行 `go mod tidy` 更新依赖

2. **运行时错误**
   - 确保系统支持图形界面
   - Linux 用户可能需要安装额外的图形库

3. **性能问题**
   - 可以调整游戏循环的时间间隔
   - 减少网格大小以提高性能

## 许可证

本项目仅供学习和娱乐使用。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个游戏！

---

**祝您游戏愉快！** 🐍🎮
