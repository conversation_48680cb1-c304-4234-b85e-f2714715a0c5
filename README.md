# 剑网三缘起茶馆日常自动化脚本

## 简介
这是一个用于按键精灵2014的剑网三缘起茶馆日常任务自动化脚本，可以自动完成茶馆的所有日常任务，包括新人茶馆任务、进阶茶馆任务和材料任务。

## 功能特点
- ✅ 自动接取茶馆任务
- ✅ 完成10个新人茶馆子任务
- ✅ 自动检测并完成进阶茶馆任务
- ✅ 处理8个材料收集任务
- ✅ 智能异常处理和重试机制
- ✅ 详细的日志记录
- ✅ 可配置的坐标和延时设置

## 支持的任务
### 新人茶馆任务 (10个)
1. 送茶予客 - 与神秘客人对话
2. 动物肉块 - 击杀草狐/草原野狼获取肉块
3. 打水煮茶 - 到水边使用水桶打水
4. 教训混混 - 击杀地痞流氓
5. 采集蜂蜜 - 采集蜂巢获取蜂蜜
6. 收集柴火 - 拾取牛粪作为燃料
7. 花茶材料 - 采集鲜花
8. 客人包裹 - 拾取遗失的包裹
9. 教训毛贼 - 击杀无礼毛贼
10. 翻晒茶叶 - 使用潮湿茶叶进行晾晒

### 进阶茶馆任务 (5个)
11. 青龙棋局 - 与伍可韵下棋
12. 风雅对诗 - 与茶馆小童对诗
13. 调皮灵猫 - 使用毛线球逗猫
14. 江湖秘闻 - 查看风云录回答问题
15. 安顿流民 - 带领流民到黑市安置

### 材料任务 (8个)
16. 五香酱肉 - 交付上等里脊肉
17. 芋粉扣肉 - 交付上等五花肉
18. 天青石任务 - 交付天青石
19. 烟雨石任务 - 交付烟雨石
20. 香叶紫苏 - 交付紫苏
21. 灵根白术 - 交付白术
22. 古香缎任务 - 交付古香缎
23. 织锦缎任务 - 交付织锦缎

## 安装和使用

### 前置要求
1. 安装按键精灵2014
2. 确保剑网三缘起游戏正常运行
3. 角色已到达可以做茶馆日常的等级

### 安装步骤
1. 下载所有脚本文件到同一目录
2. 打开按键精灵2014
3. 导入 `jx3_chaguan_daily.q` 脚本文件

### 使用前配置
1. **坐标配置**：
   - 启动游戏并进入茶馆区域
   - 使用按键精灵的坐标获取工具记录各个NPC的坐标
   - 修改 `config.txt` 文件中的坐标设置

2. **延时配置**：
   - 根据电脑性能和网络状况调整延时设置
   - 配置较慢的电脑建议增加延时时间

3. **颜色配置**：
   - 使用按键精灵的颜色获取工具记录关键界面元素的颜色
   - 用于判断任务状态和界面变化

### 快速开始
1. **新用户**：运行 `快速设置向导.q` 进行初始配置
2. **图形界面**：使用 `启动器.q` 进行日常操作
3. **直接运行**：执行 `jx3_chaguan_daily.q` 主脚本

### 运行脚本
1. 确保角色位于茶馆区域（太原或阴山大草原）
2. 在按键精灵中启动脚本
3. 脚本会自动执行所有茶馆日常任务
4. 查看 `jx3_log.txt` 文件了解执行状态

## 配置文件说明

### config.txt 配置项
- **[坐标]** - 各个NPC和任务点的屏幕坐标
- **[延时]** - 各种操作的等待时间
- **[颜色]** - 用于状态检测的颜色值
- **[设置]** - 脚本运行的基本设置
- **[快捷键]** - 游戏内快捷键设置
- **[材料物品]** - 材料任务所需物品名称
- **[对诗答案]** - 风雅对诗任务的答案库
- **[异常处理]** - 异常情况的处理方式

## 注意事项

### 重要提醒
⚠️ **使用脚本前请仔细阅读以下内容**

1. **坐标适配**：脚本中的坐标需要根据你的屏幕分辨率和游戏设置进行调整
2. **网络延迟**：根据网络状况适当调整延时设置
3. **游戏更新**：游戏更新后可能需要重新调整坐标和颜色设置
4. **材料准备**：材料任务需要提前准备相应的材料物品
5. **安全使用**：建议在人少的时间段使用，避免被其他玩家干扰

### 故障排除
1. **脚本无法启动**：
   - 检查游戏窗口是否正确识别
   - 确认按键精灵版本兼容性

2. **坐标点击错误**：
   - 重新获取并配置正确的坐标
   - 检查游戏界面缩放设置

3. **任务执行失败**：
   - 查看日志文件了解具体错误
   - 检查网络连接和游戏状态

4. **材料任务跳过**：
   - 确认背包中有相应材料
   - 检查材料物品名称配置

## 自定义和扩展

### 添加新任务
1. 在脚本中添加新的任务执行函数
2. 在主执行流程中调用新函数
3. 更新配置文件中的相关设置

### 优化脚本性能
1. 调整延时设置以适应你的电脑性能
2. 优化坐标点击的精确度
3. 添加更多的异常处理逻辑

## 版本历史
- v1.0 - 初始版本，支持完整的茶馆日常任务

## 免责声明
本脚本仅供学习和研究使用，使用者需要自行承担使用风险。请遵守游戏服务条款，合理使用自动化工具。

## 技术支持
如果在使用过程中遇到问题，请：
1. 首先查看日志文件 `jx3_log.txt`
2. 检查配置文件设置是否正确
3. 确认游戏版本和脚本兼容性

---
**祝您游戏愉快！**
